"""
Results Manager for Task Order Optimization

This module provides comprehensive result organization, logging, and analysis
for the continual learning task order optimization experiments.

CRITICAL: This module ONLY works with real experimental data.
NO fake or simulated data is generated.
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import numpy as np

from order_search_framework import ExperimentResult


class ResultsManager:
    """Manages experiment results with organized output and comprehensive logging."""
    
    def __init__(self, output_dir: str = "task_order_optimization/results"):
        """Initialize the results manager."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for organized output
        (self.output_dir / "experiments").mkdir(exist_ok=True)
        (self.output_dir / "summaries").mkdir(exist_ok=True)
        (self.output_dir / "logs").mkdir(exist_ok=True)
        (self.output_dir / "matrices").mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # Setup detailed logging
        self._setup_detailed_logging()
    
    def _setup_detailed_logging(self):
        """Setup comprehensive logging for results."""
        
        # Create detailed log file
        log_file = self.output_dir / "logs" / f"search_detailed_{time.strftime('%Y%m%d_%H%M%S')}.log"
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)
        
        self.logger.info("=" * 80)
        self.logger.info("RESULTS MANAGER INITIALIZED")
        self.logger.info(f"Output directory: {self.output_dir}")
        self.logger.info("=" * 80)
    
    def save_experiment_result(self, experiment_id: int, result: ExperimentResult, 
                             performance_matrix: np.ndarray, duration: float):
        """Save detailed results for a single experiment."""
        
        self.logger.info(f"💾 Saving experiment {experiment_id} results...")
        
        try:
            # Save performance matrix
            matrix_file = self.output_dir / "matrices" / f"experiment_{experiment_id}_matrix.npy"
            np.save(matrix_file, performance_matrix)
            
            # Save detailed experiment data
            experiment_data = {
                'experiment_id': experiment_id,
                'task_order': result.task_order,
                'objective_score': result.objective_score,
                'metrics': result.metrics,
                'performance_matrix': performance_matrix.tolist(),
                'duration_seconds': duration,
                'timestamp': time.time(),
                'timestamp_human': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            experiment_file = self.output_dir / "experiments" / f"experiment_{experiment_id}.json"
            with open(experiment_file, 'w') as f:
                json.dump(experiment_data, f, indent=2, default=str)
            
            # Create human-readable summary
            summary_file = self.output_dir / "experiments" / f"experiment_{experiment_id}_summary.txt"
            self._create_experiment_summary(experiment_data, summary_file)
            
            self.logger.info(f"✅ Experiment {experiment_id} results saved successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving experiment {experiment_id} results: {e}")
    
    def _create_experiment_summary(self, experiment_data: Dict[str, Any], output_file: Path):
        """Create a human-readable experiment summary."""
        
        with open(output_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write(f"EXPERIMENT {experiment_data['experiment_id']} SUMMARY\n")
            f.write("=" * 80 + "\n")
            f.write(f"Timestamp: {experiment_data['timestamp_human']}\n")
            f.write(f"Duration: {experiment_data['duration_seconds']:.1f} seconds\n")
            f.write(f"Objective Score: {experiment_data['objective_score']:.4f}\n")
            f.write("\n")
            
            f.write("TASK ORDER:\n")
            task_order = experiment_data['task_order']
            f.write(" → ".join([t.replace('q_', '') for t in task_order]) + "\n")
            f.write("\n")
            
            f.write("PERFORMANCE METRICS:\n")
            metrics = experiment_data['metrics']
            f.write(f"  Final Average Accuracy: {metrics.get('final_avg_accuracy', 'N/A'):.2f}%\n")
            f.write(f"  Average Forgetting: {metrics.get('avg_forgetting', 'N/A'):.2f}\n")
            f.write(f"  Average Forward Transfer: {metrics.get('avg_forward_transfer', 'N/A'):.2f}\n")
            f.write("\n")
            
            f.write("PERFORMANCE MATRIX:\n")
            matrix = np.array(experiment_data['performance_matrix'])
            f.write(f"Shape: {matrix.shape}\n")
            f.write("Matrix (rows=training steps, cols=tasks):\n")
            
            # Write matrix with task headers
            task_headers = ['recognition', 'causal', 'color', 'commonsense', 'subcategory', 
                          'location', 'count', 'action', 'type', 'judge']
            f.write("Step\t" + "\t".join(task_headers) + "\tAvg\n")
            
            for step in range(matrix.shape[0]):
                row_values = []
                step_sum = 0
                step_count = 0
                
                for col in range(matrix.shape[1]):
                    val = matrix[step, col]
                    row_values.append(f"{val:.1f}")
                    if val > 0:  # Only count non-zero values for average
                        step_sum += val
                        step_count += 1
                
                step_avg = step_sum / step_count if step_count > 0 else 0
                f.write(f"{step+1}\t" + "\t".join(row_values) + f"\t{step_avg:.1f}\n")
    
    def save_search_summary(self, all_results: List[ExperimentResult], 
                          search_metadata: Dict[str, Any]):
        """Save comprehensive search summary."""
        
        self.logger.info("📋 Creating comprehensive search summary...")
        
        try:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            
            # Save JSON summary
            summary_data = {
                'search_metadata': search_metadata,
                'total_experiments': len(all_results),
                'all_results': [
                    {
                        'experiment_id': i + 1,
                        'task_order': result.task_order,
                        'objective_score': result.objective_score,
                        'metrics': result.metrics
                    }
                    for i, result in enumerate(all_results)
                ],
                'best_result': self._get_best_result_data(all_results),
                'statistics': self._calculate_statistics(all_results),
                'timestamp': time.time(),
                'timestamp_human': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            json_file = self.output_dir / "summaries" / f"search_summary_{timestamp}.json"
            with open(json_file, 'w') as f:
                json.dump(summary_data, f, indent=2, default=str)
            
            # Create human-readable summary
            text_file = self.output_dir / "summaries" / f"search_summary_{timestamp}.txt"
            self._create_text_summary(summary_data, text_file)
            
            # Create ranking table
            ranking_file = self.output_dir / "summaries" / f"experiment_ranking_{timestamp}.txt"
            self._create_ranking_table(all_results, ranking_file)
            
            self.logger.info(f"✅ Search summary saved: {json_file}")
            
            return json_file
            
        except Exception as e:
            self.logger.error(f"❌ Error saving search summary: {e}")
            return None
    
    def _get_best_result_data(self, results: List[ExperimentResult]) -> Dict[str, Any]:
        """Get the best result data."""
        if not results:
            return {}
        
        best_result = max(results, key=lambda r: r.objective_score)
        return {
            'task_order': best_result.task_order,
            'objective_score': best_result.objective_score,
            'metrics': best_result.metrics
        }
    
    def _calculate_statistics(self, results: List[ExperimentResult]) -> Dict[str, float]:
        """Calculate summary statistics."""
        if not results:
            return {}
        
        scores = [r.objective_score for r in results]
        
        return {
            'best_score': max(scores),
            'worst_score': min(scores),
            'mean_score': np.mean(scores),
            'std_score': np.std(scores),
            'median_score': np.median(scores)
        }
    
    def _create_text_summary(self, summary_data: Dict[str, Any], output_file: Path):
        """Create human-readable text summary."""
        
        with open(output_file, 'w') as f:
            f.write("=" * 100 + "\n")
            f.write("TASK ORDER OPTIMIZATION SEARCH SUMMARY\n")
            f.write("=" * 100 + "\n")
            f.write(f"Search completed: {summary_data['timestamp_human']}\n")
            f.write(f"Total experiments: {summary_data['total_experiments']}\n")
            f.write(f"Search duration: {summary_data['search_metadata'].get('total_time_seconds', 0) / 3600:.1f} hours\n")
            f.write("\n")
            
            # Best result
            best = summary_data['best_result']
            if best:
                f.write("BEST RESULT:\n")
                f.write(f"  Task Order: {' → '.join([t.replace('q_', '') for t in best['task_order']])}\n")
                f.write(f"  Objective Score: {best['objective_score']:.4f}\n")
                f.write(f"  Final Accuracy: {best['metrics'].get('final_avg_accuracy', 'N/A'):.2f}%\n")
                f.write(f"  Avg Forgetting: {best['metrics'].get('avg_forgetting', 'N/A'):.2f}\n")
                f.write("\n")
            
            # Statistics
            stats = summary_data['statistics']
            if stats:
                f.write("STATISTICS:\n")
                f.write(f"  Best Score: {stats['best_score']:.4f}\n")
                f.write(f"  Worst Score: {stats['worst_score']:.4f}\n")
                f.write(f"  Mean Score: {stats['mean_score']:.4f}\n")
                f.write(f"  Std Dev: {stats['std_score']:.4f}\n")
                f.write(f"  Median Score: {stats['median_score']:.4f}\n")
    
    def _create_ranking_table(self, results: List[ExperimentResult], output_file: Path):
        """Create experiment ranking table."""
        
        # Sort by objective score
        sorted_results = sorted(results, key=lambda r: r.objective_score, reverse=True)
        
        with open(output_file, 'w') as f:
            f.write("EXPERIMENT RANKING TABLE\n")
            f.write("=" * 120 + "\n")
            f.write(f"{'Rank':<4} {'Exp':<3} {'Obj Score':<10} {'Final Acc':<9} {'Forgetting':<10} {'Transfer':<9} {'Task Order (first 4)':<40}\n")
            f.write("-" * 120 + "\n")
            
            for rank, result in enumerate(sorted_results, 1):
                first_four = ' → '.join([t.replace('q_', '') for t in result.task_order[:4]])
                
                f.write(f"{rank:<4} {rank:<3} {result.objective_score:<10.3f} "
                       f"{result.metrics.get('final_avg_accuracy', 0):<9.2f} "
                       f"{result.metrics.get('avg_forgetting', 0):<10.2f} "
                       f"{result.metrics.get('avg_forward_transfer', 0):<9.2f} "
                       f"{first_four:<40}\n")
    
    def log_epoch_summary(self, epoch: int, task: str, metrics: Dict[str, float]):
        """Log end-of-epoch summary."""
        
        self.logger.info("─" * 60)
        self.logger.info(f"📊 EPOCH {epoch} SUMMARY - Task: {task.replace('q_', '')}")
        self.logger.info(f"   Accuracy: {metrics.get('accuracy', 0):.2f}%")
        self.logger.info(f"   Loss: {metrics.get('loss', 0):.4f}")
        if 'forgetting' in metrics:
            self.logger.info(f"   Forgetting: {metrics['forgetting']:.2f}")
        if 'transfer' in metrics:
            self.logger.info(f"   Transfer: {metrics['transfer']:.2f}")
        self.logger.info("─" * 60)
