"""
Test script to verify the task order optimization integration works correctly.
"""

import sys
import numpy as np
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from task_order_optimization.order_search_framework import (
    ExperimentDataStore, 
    HeuristicProposer, 
    ContinualLearningAnalyzer
)
from task_order_optimization.cl_experiment_adapter import run_my_actual_cl_experiment
from Question_type import set_custom_task_order, get_task_order, reset_task_order


def test_question_type_integration():
    """Test the Question_type.py integration."""
    print("Testing Question_type.py integration...")
    
    # Test setting custom task order
    original_order = get_task_order()
    print(f"Original task order: {original_order}")
    
    custom_order = ['q_color', 'q_location', 'q_recognition']
    set_custom_task_order(custom_order)
    
    current_order = get_task_order()
    print(f"Custom task order: {current_order}")
    
    assert current_order == custom_order, "Custom task order not set correctly"
    
    # Test reset
    reset_task_order()
    reset_order = get_task_order()
    print(f"Reset task order: {reset_order}")
    
    assert reset_order == original_order, "Task order not reset correctly"
    
    print("✓ Question_type.py integration test passed\n")


def test_framework_components():
    """Test the optimization framework components."""
    print("Testing framework components...")
    
    # Test data
    all_tasks = ['q_recognition', 'q_color', 'q_location', 'q_count']
    task_order = ['q_recognition', 'q_color', 'q_location', 'q_count']
    
    # Create a sample performance matrix
    performance_matrix = np.array([
        [75.0, 0.0, 0.0, 0.0],    # After training q_recognition
        [70.0, 80.0, 0.0, 0.0],   # After training q_color
        [65.0, 75.0, 65.0, 0.0],  # After training q_location
        [60.0, 70.0, 60.0, 70.0]  # After training q_count
    ])
    
    # Test analyzer
    analyzer = ContinualLearningAnalyzer()
    metrics = analyzer.calculate_metrics(performance_matrix, task_order, all_tasks)
    print(f"Calculated metrics: {metrics}")
    
    assert 'final_avg_accuracy' in metrics, "Missing final_avg_accuracy metric"
    assert 'avg_forgetting' in metrics, "Missing avg_forgetting metric"
    
    # Test data store
    objective_weights = {
        'final_avg_accuracy': 1.0,
        'avg_forgetting': -0.5,
        'avg_forward_transfer': 0.3,
        'learning_curve_area': 0.2,
        'stability': 0.1
    }
    
    data_store = ExperimentDataStore(all_tasks)
    data_store.add_experiment(task_order, performance_matrix, all_tasks, objective_weights)
    
    assert len(data_store.results) == 1, "Experiment not added to data store"
    
    best_result = data_store.get_best_result()
    assert best_result is not None, "No best result found"
    
    # Test proposer
    proposer = HeuristicProposer(all_tasks)
    proposed_orders = proposer.propose_orders(data_store, 3)
    
    assert len(proposed_orders) == 3, "Wrong number of proposed orders"
    assert all(len(order) == len(all_tasks) for order in proposed_orders), "Invalid order lengths"
    
    print("✓ Framework components test passed\n")


def test_cl_experiment_adapter():
    """Test the CL experiment adapter (with placeholder)."""
    print("Testing CL experiment adapter...")
    
    task_order = ['q_recognition', 'q_color', 'q_location']
    canonical_tasks = ['q_recognition', 'q_causal', 'q_color', 'q_commonsense', 
                      'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge']
    
    # Run experiment (will use placeholder implementation)
    performance_matrix = run_my_actual_cl_experiment(task_order, canonical_tasks)
    
    assert performance_matrix is not None, "Experiment returned None"
    assert performance_matrix.shape == (len(task_order), len(canonical_tasks)), \
        f"Wrong matrix shape: {performance_matrix.shape}"
    
    # Check that the matrix has reasonable values
    assert np.all(performance_matrix >= 0), "Negative values in performance matrix"
    assert np.all(performance_matrix <= 100), "Values > 100 in performance matrix"
    
    # Check that untrained tasks have zero performance
    for step in range(len(task_order)):
        for col, task in enumerate(canonical_tasks):
            if task in task_order:
                trained_step = task_order.index(task)
                if trained_step > step:
                    assert performance_matrix[step, col] == 0.0, \
                        f"Non-zero performance for untrained task {task} at step {step}"
    
    print(f"Performance matrix shape: {performance_matrix.shape}")
    print(f"Sample values: {performance_matrix[0, :5]}")
    print("✓ CL experiment adapter test passed\n")


def test_end_to_end_mini_search():
    """Test a mini end-to-end search with 2 orders."""
    print("Testing mini end-to-end search...")
    
    from task_order_optimization.main_search_loop import TaskOrderSearchManager
    
    # Small test configuration
    all_tasks = ['q_recognition', 'q_color', 'q_location', 'q_count']
    objective_weights = {
        'final_avg_accuracy': 1.0,
        'avg_forgetting': -0.5,
        'avg_forward_transfer': 0.3,
        'learning_curve_area': 0.2,
        'stability': 0.1
    }
    
    # Create search manager with minimal settings
    search_manager = TaskOrderSearchManager(
        all_tasks=all_tasks,
        objective_weights=objective_weights,
        max_iterations=1,  # Just one iteration
        orders_per_iteration=2  # Just two orders
    )
    
    # Run mini search
    initial_orders = [
        all_tasks.copy(),
        list(reversed(all_tasks))
    ]
    
    results = search_manager.run_search(initial_orders=initial_orders)
    
    # Verify results
    assert 'search_metadata' in results, "Missing search metadata"
    assert 'best_result' in results, "Missing best result"
    assert 'summary_statistics' in results, "Missing summary statistics"
    
    assert results['search_metadata']['total_experiments'] >= 2, "Not enough experiments run"
    assert results['best_result']['task_order'] is not None, "No best task order found"
    
    print(f"Mini search completed with {results['search_metadata']['total_experiments']} experiments")
    print(f"Best order: {results['best_result']['task_order']}")
    print(f"Best score: {results['best_result']['objective_score']:.4f}")
    print("✓ End-to-end mini search test passed\n")


def main():
    """Run all tests."""
    print("="*60)
    print("RUNNING TASK ORDER OPTIMIZATION INTEGRATION TESTS")
    print("="*60)
    def test_configuration_validation():
        """Test configuration validation and error handling."""
        print("Testing configuration validation...")
        
        from task_order_optimization.main_search_loop import TaskOrderSearchManager
        
        # Test invalid task list
        try:
            TaskOrderSearchManager(
                all_tasks=[],  # Empty task list
                objective_weights={'final_avg_accuracy': 1.0},
                max_iterations=1
            )
            assert False, "Should have raised error for empty task list"
        except ValueError:
            pass
        
        # Test invalid objective weights
        try:
            TaskOrderSearchManager(
                all_tasks=['q_recognition', 'q_color'],
                objective_weights={},  # Empty weights
                max_iterations=1
            )
            assert False, "Should have raised error for empty objective weights"
        except ValueError:
            pass
        
        # Test invalid iterations
        try:
            TaskOrderSearchManager(
                all_tasks=['q_recognition', 'q_color'],
                objective_weights={'final_avg_accuracy': 1.0},
                max_iterations=0  # Invalid iteration count
            )
            assert False, "Should have raised error for zero iterations"
        except ValueError:
            pass
        
        print("✓ Configuration validation test passed\n")


    def test_performance_matrix_validation():
        """Test performance matrix validation and edge cases."""
        print("Testing performance matrix validation...")
        
        from task_order_optimization.order_search_framework import ContinualLearningAnalyzer
        
        analyzer = ContinualLearningAnalyzer()
        all_tasks = ['q_recognition', 'q_color', 'q_location']
        task_order = ['q_recognition', 'q_color', 'q_location']
        
        # Test with invalid matrix dimensions
        try:
            invalid_matrix = np.array([[1, 2], [3, 4]])  # Wrong dimensions
            analyzer.calculate_metrics(invalid_matrix, task_order, all_tasks)
            assert False, "Should have raised error for invalid matrix dimensions"
        except (ValueError, AssertionError):
            pass
        
        # Test with negative values
        negative_matrix = np.array([
            [-10.0, 0.0, 0.0],
            [50.0, 60.0, 0.0],
            [40.0, 55.0, 70.0]
        ])
        metrics = analyzer.calculate_metrics(negative_matrix, task_order, all_tasks)
        assert metrics is not None, "Should handle negative values gracefully"
        
        # Test with extreme values
        extreme_matrix = np.array([
            [100.0, 0.0, 0.0],
            [0.0, 100.0, 0.0],
            [0.0, 0.0, 100.0]
        ])
        metrics = analyzer.calculate_metrics(extreme_matrix, task_order, all_tasks)
        assert metrics['avg_forgetting'] == 100.0, "Should handle extreme forgetting correctly"
        
        print("✓ Performance matrix validation test passed\n")


    def test_heuristic_proposer_edge_cases():
        """Test heuristic proposer with various edge cases."""
        print("Testing heuristic proposer edge cases...")
        
        from task_order_optimization.order_search_framework import HeuristicProposer, ExperimentDataStore
        
        # Test with minimal task set
        minimal_tasks = ['q_recognition']
        proposer = HeuristicProposer(minimal_tasks)
        data_store = ExperimentDataStore(minimal_tasks)
        
        proposed_orders = proposer.propose_orders(data_store, 3)
        assert len(proposed_orders) <= 3, "Should not propose more orders than requested"
        assert all(order == minimal_tasks for order in proposed_orders), "All orders should be identical for single task"
        
        # Test with empty data store
        empty_tasks = ['q_recognition', 'q_color']
        proposer = HeuristicProposer(empty_tasks)
        empty_store = ExperimentDataStore(empty_tasks)
        
        proposed_orders = proposer.propose_orders(empty_store, 2)
        assert len(proposed_orders) == 2, "Should propose fallback orders for empty store"
        
        # Test with requesting more orders than possible
        two_tasks = ['q_recognition', 'q_color']
        proposer = HeuristicProposer(two_tasks)
        data_store = ExperimentDataStore(two_tasks)
        
        proposed_orders = proposer.propose_orders(data_store, 10)  # More than 2! = 2
        assert len(proposed_orders) <= 10, "Should not exceed maximum possible permutations"
        
        print("✓ Heuristic proposer edge cases test passed\n")


    def test_data_store_operations():
        """Test data store operations and state management."""
        print("Testing data store operations...")
        
        from task_order_optimization.order_search_framework import ExperimentDataStore
        
        all_tasks = ['q_recognition', 'q_color', 'q_location']
        data_store = ExperimentDataStore(all_tasks)
        
        # Test empty state
        assert len(data_store.results) == 0, "New data store should be empty"
        assert data_store.get_best_result() is None, "Empty store should return None for best result"
        
        # Add multiple experiments
        objective_weights = {'final_avg_accuracy': 1.0, 'avg_forgetting': -0.5}
        
        matrix1 = np.array([[80, 0, 0], [70, 75, 0], [65, 70, 80]])
        matrix2 = np.array([[70, 0, 0], [75, 85, 0], [70, 80, 75]])
        
        order1 = ['q_recognition', 'q_color', 'q_location']
        order2 = ['q_color', 'q_recognition', 'q_location']
        
        data_store.add_experiment(order1, matrix1, all_tasks, objective_weights)
        data_store.add_experiment(order2, matrix2, all_tasks, objective_weights)
        
        assert len(data_store.results) == 2, "Should have two experiments"
        
        best_result = data_store.get_best_result()
        assert best_result is not None, "Should have a best result"
        assert 'task_order' in best_result, "Best result should have task order"
        assert 'objective_score' in best_result, "Best result should have objective score"
        
        # Test duplicate detection
        initial_count = len(data_store.results)
        data_store.add_experiment(order1, matrix1, all_tasks, objective_weights)  # Duplicate
        assert len(data_store.results) == initial_count, "Should not add duplicate experiments"
        
        print("✓ Data store operations test passed\n")


    def test_metrics_calculation_edge_cases():
        """Test metrics calculation with edge cases."""
        print("Testing metrics calculation edge cases...")
        
        from task_order_optimization.order_search_framework import ContinualLearningAnalyzer
        
        analyzer = ContinualLearningAnalyzer()
        
        # Test perfect performance (no forgetting)
        perfect_matrix = np.array([
            [100.0, 0.0, 0.0],
            [100.0, 100.0, 0.0],
            [100.0, 100.0, 100.0]
        ])
        task_order = ['q_recognition', 'q_color', 'q_location']
        all_tasks = ['q_recognition', 'q_color', 'q_location']
        
        metrics = analyzer.calculate_metrics(perfect_matrix, task_order, all_tasks)
        assert metrics['avg_forgetting'] == 0.0, "Perfect performance should have zero forgetting"
        assert metrics['final_avg_accuracy'] == 100.0, "Perfect performance should have 100% accuracy"
        
        # Test complete forgetting
        forgetting_matrix = np.array([
            [100.0, 0.0, 0.0],
            [0.0, 100.0, 0.0],
            [0.0, 0.0, 100.0]
        ])
        
        metrics = analyzer.calculate_metrics(forgetting_matrix, task_order, all_tasks)
        assert metrics['avg_forgetting'] == 100.0, "Complete forgetting should be 100%"
        
        # Test with single task
        single_matrix = np.array([[75.0]])
        single_order = ['q_recognition']
        single_tasks = ['q_recognition']
        
        metrics = analyzer.calculate_metrics(single_matrix, single_order, single_tasks)
        assert metrics['final_avg_accuracy'] == 75.0, "Single task accuracy should match"
        assert metrics['avg_forgetting'] == 0.0, "Single task should have no forgetting"
        
        print("✓ Metrics calculation edge cases test passed\n")


    def test_search_manager_state_management():
        """Test search manager state management and persistence."""
        print("Testing search manager state management...")
        
        from task_order_optimization.main_search_loop import TaskOrderSearchManager
        
        all_tasks = ['q_recognition', 'q_color']
        objective_weights = {'final_avg_accuracy': 1.0}
        
        search_manager = TaskOrderSearchManager(
            all_tasks=all_tasks,
            objective_weights=objective_weights,
            max_iterations=1,
            orders_per_iteration=2
        )
        
        # Test initial state
        assert hasattr(search_manager, 'data_store'), "Should have data store"
        assert hasattr(search_manager, 'proposer'), "Should have proposer"
        assert hasattr(search_manager, 'analyzer'), "Should have analyzer"
        
        # Test configuration access
        assert search_manager.all_tasks == all_tasks, "Should store task list"
        assert search_manager.objective_weights == objective_weights, "Should store weights"
        
        # Run a mini search
        initial_orders = [all_tasks, list(reversed(all_tasks))]
        results = search_manager.run_search(initial_orders=initial_orders)
        
        # Verify state after search
        assert len(search_manager.data_store.results) > 0, "Should have experiment results"
        assert results['search_metadata']['total_experiments'] > 0, "Should have run experiments"
        
        print("✓ Search manager state management test passed\n")


    def test_error_handling_and_recovery():
        """Test error handling and recovery mechanisms."""
        print("Testing error handling and recovery...")
        
        from task_order_optimization.order_search_framework import ContinualLearningAnalyzer
        
        analyzer = ContinualLearningAnalyzer()
        
        # Test with mismatched task lists
        matrix = np.array([[75, 0], [70, 80]])
        task_order = ['q_recognition', 'q_color']
        all_tasks = ['q_recognition', 'q_location']  # Mismatch
        
        try:
            metrics = analyzer.calculate_metrics(matrix, task_order, all_tasks)
            # Should handle gracefully or raise appropriate error
        except (ValueError, KeyError, AssertionError):
            pass  # Expected behavior
        
        # Test with NaN values
        nan_matrix = np.array([[np.nan, 0], [70, 80]])
        try:
            metrics = analyzer.calculate_metrics(nan_matrix, task_order, ['q_recognition', 'q_color'])
            # Should handle NaN values appropriately
            assert not np.isnan(metrics['final_avg_accuracy']), "Should not return NaN metrics"
        except (ValueError, AssertionError):
            pass  # Expected behavior for invalid input
        
        # Test with infinite values
        inf_matrix = np.array([[np.inf, 0], [70, 80]])
        try:
            metrics = analyzer.calculate_metrics(inf_matrix, task_order, ['q_recognition', 'q_color'])
            # Should handle infinite values appropriately
        except (ValueError, AssertionError):
            pass  # Expected behavior for invalid input
        
        print("✓ Error handling and recovery test passed\n")
