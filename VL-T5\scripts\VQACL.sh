# The name of experiment
name=VQAv2_Our

output=snap/$name

# Set a valid master port (fixed or random)
export MASTER_PORT=${MASTER_PORT:-$((10000 + RANDOM % 50000))}

PYTHONPATH=$PYTHONPATH:./src \
torchrun \
    --nproc_per_node=$1 \
    --master_port=$MASTER_PORT \
    src/vqacl.py \
        --distributed --multiGPU \
        --train karpathy_train \
        --valid karpathy_val \
        --test karpathy_test \
        --optim adamw \
        --warmup_ratio 0.1 \
        --clip_grad_norm 5 \
        --lr 1e-4 \
        --epochs 3 \
        --num_workers 4 \
        --backbone 't5-base' \
        --output $output ${@:2} \
        --num_beams 5 \
        --batch_size 80 \
        --valid_batch_size 100 \
        --from_scratch