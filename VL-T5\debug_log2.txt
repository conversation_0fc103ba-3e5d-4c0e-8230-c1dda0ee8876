q_color {'Test/overall': 61.0, 'Test/topk_optimal': 61.41, 'Test/topk_not_optimal': 23.75, 'Test_Qtypes/what color is the': 60.17, 'Test_Qtypes/what color': 61.72, 'Test_Qtypes/what color are the': 60.0, 'Test_Qtypes/what is the color of the': 69.18, 'Test_Qtypes/what color is': 66.27, 'Test_Atypes/other': 61.0}
q_color {'Test/overall': 57.38, 'Test/topk_optimal': 57.82, 'Test/topk_not_optimal': 17.5, 'Test_Qtypes/what color is the': 58.51, 'Test_Qtypes/what color': 54.97, 'Test_Qtypes/what color are the': 49.39, 'Test_Qtypes/what is the color of the': 66.94, 'Test_Qtypes/what color is': 60.77, 'Test_Atypes/other': 57.38}
q_count {'Test/overall': 36.19, 'Test/topk_optimal': 36.51, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 36.26, 'Test_Qtypes/how many people are': 40.61, 'Test_Qtypes/how many people are in': 40.53, 'Test_Qtypes/what number is': 9.52, 'Test_Atypes/number': 36.49, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 27.19, 'Test/topk_optimal': 31.15, 'Test/topk_not_optimal': 2.78, 'Test_Qtypes/what': 25.97, 'Test_Qtypes/what is the': 27.6, 'Test_Qtypes/what is on the': 26.45, 'Test_Qtypes/what is': 23.94, 'Test_Qtypes/what is this': 39.54, 'Test_Qtypes/what are the': 29.88, 'Test_Qtypes/what are': 35.87, 'Test_Qtypes/what is in the': 34.06, 'Test_Qtypes/who is': 17.5, 'Test_Qtypes/what is the name': 4.52, 'Test_Qtypes/which': 33.73, 'Test_Qtypes/what does the': 20.64, 'Test_Atypes/other': 27.97, 'Test_Atypes/number': 5.82, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 25.02, 'Test/topk_optimal': 28.67, 'Test/topk_not_optimal': 2.55, 'Test_Qtypes/what': 24.16, 'Test_Qtypes/what is the': 25.23, 'Test_Qtypes/what is on the': 24.89, 'Test_Qtypes/what is': 19.28, 'Test_Qtypes/what is this': 36.22, 'Test_Qtypes/what are the': 27.81, 'Test_Qtypes/what are': 35.87, 'Test_Qtypes/what is in the': 31.61, 'Test_Qtypes/who is': 23.33, 'Test_Qtypes/what is the name': 5.75, 'Test_Qtypes/which': 32.84, 'Test_Qtypes/what does the': 16.99, 'Test_Atypes/other': 25.63, 'Test_Atypes/number': 8.32, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 30.39, 'Test/topk_optimal': 34.37, 'Test/topk_not_optimal': 4.76, 'Test_Qtypes/where are the': 23.04, 'Test_Qtypes/where is the': 25.95, 'Test_Qtypes/what room is': 83.64, 'Test_Atypes/other': 30.44, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 15.81, 'Test/topk_optimal': 18.03, 'Test/topk_not_optimal': 2.21, 'Test_Qtypes/what': 15.61, 'Test_Qtypes/what is the': 16.62, 'Test_Qtypes/what is on the': 12.29, 'Test_Qtypes/what is': 10.97, 'Test_Qtypes/what is this': 23.67, 'Test_Qtypes/what are the': 14.82, 'Test_Qtypes/what are': 20.52, 'Test_Qtypes/what is in the': 16.68, 'Test_Qtypes/who is': 24.35, 'Test_Qtypes/what is the name': 2.74, 'Test_Qtypes/which': 25.13, 'Test_Qtypes/what does the': 11.32, 'Test_Atypes/other': 16.25, 'Test_Atypes/number': 3.83, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 23.18, 'Test/topk_optimal': 25.75, 'Test/topk_not_optimal': 6.59, 'Test_Qtypes/where are the': 15.25, 'Test_Qtypes/where is the': 18.54, 'Test_Qtypes/what room is': 79.45, 'Test_Atypes/other': 23.21, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 63.35, 'Test/topk_optimal': 63.84, 'Test/topk_not_optimal': 5.41, 'Test_Qtypes/is there': 63.14, 'Test_Qtypes/is the': 64.37, 'Test_Qtypes/is this a': 63.76, 'Test_Qtypes/is there a': 61.67, 'Test_Qtypes/are the': 59.21, 'Test_Qtypes/is this': 64.17, 'Test_Qtypes/was': 63.37, 'Test_Qtypes/is': 61.74, 'Test_Qtypes/is it': 69.56, 'Test_Qtypes/are these': 61.64, 'Test_Qtypes/are there': 65.59, 'Test_Qtypes/is this an': 66.02, 'Test_Qtypes/are': 58.89, 'Test_Qtypes/is that a': 57.6, 'Test_Qtypes/are there any': 61.63, 'Test_Atypes/yes_no': 64.99, 'Test_Atypes/other': 42.21, 'Test_Atypes/number': 58.0}
q_recognition {'Test/overall': 19.15, 'Test/topk_optimal': 21.88, 'Test/topk_not_optimal': 2.33, 'Test_Qtypes/what': 17.18, 'Test_Qtypes/what is the': 21.03, 'Test_Qtypes/what is on the': 16.62, 'Test_Qtypes/what is': 14.49, 'Test_Qtypes/what is this': 32.4, 'Test_Qtypes/what are the': 23.27, 'Test_Qtypes/what are': 23.26, 'Test_Qtypes/what is in the': 21.2, 'Test_Qtypes/who is': 20.19, 'Test_Qtypes/what is the name': 5.89, 'Test_Qtypes/which': 26.05, 'Test_Qtypes/what does the': 12.92, 'Test_Atypes/other': 19.73, 'Test_Atypes/number': 3.21, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 26.43, 'Test/topk_optimal': 29.85, 'Test/topk_not_optimal': 4.39, 'Test_Qtypes/where are the': 22.09, 'Test_Qtypes/where is the': 21.06, 'Test_Qtypes/what room is': 77.82, 'Test_Atypes/other': 26.48, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 65.44, 'Test/topk_optimal': 65.96, 'Test/topk_not_optimal': 4.43, 'Test_Qtypes/is there': 62.81, 'Test_Qtypes/is the': 66.23, 'Test_Qtypes/is this a': 67.26, 'Test_Qtypes/is there a': 62.2, 'Test_Qtypes/are the': 63.28, 'Test_Qtypes/is this': 67.23, 'Test_Qtypes/was': 67.55, 'Test_Qtypes/is': 65.45, 'Test_Qtypes/is it': 67.41, 'Test_Qtypes/are these': 63.36, 'Test_Qtypes/are there': 66.54, 'Test_Qtypes/is this an': 67.04, 'Test_Qtypes/are': 61.88, 'Test_Qtypes/is that a': 59.69, 'Test_Qtypes/are there any': 64.74, 'Test_Atypes/yes_no': 67.5, 'Test_Atypes/other': 38.9, 'Test_Atypes/number': 52.0}
q_commonsense {'Test/overall': 66.01, 'Test/topk_optimal': 66.17, 'Test/topk_not_optimal': 30.0, 'Test_Qtypes/do': 63.65, 'Test_Qtypes/does the': 65.78, 'Test_Qtypes/does this': 69.07, 'Test_Qtypes/can you': 63.15, 'Test_Qtypes/has': 56.83, 'Test_Qtypes/could': 77.61, 'Test_Qtypes/do you': 69.64, 'Test_Atypes/yes_no': 67.05, 'Test_Atypes/other': 26.3, 'Test_Atypes/number': 45.0}
q_recognition {'Test/overall': 15.67, 'Test/topk_optimal': 17.97, 'Test/topk_not_optimal': 1.56, 'Test_Qtypes/what': 17.96, 'Test_Qtypes/what is the': 14.04, 'Test_Qtypes/what is on the': 15.02, 'Test_Qtypes/what is': 11.79, 'Test_Qtypes/what is this': 21.33, 'Test_Qtypes/what are the': 17.64, 'Test_Qtypes/what are': 18.49, 'Test_Qtypes/what is in the': 18.53, 'Test_Qtypes/who is': 17.31, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 15.72, 'Test_Qtypes/what does the': 10.46, 'Test_Atypes/other': 16.1, 'Test_Atypes/number': 3.98, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 24.76, 'Test/topk_optimal': 28.15, 'Test/topk_not_optimal': 2.93, 'Test_Qtypes/where are the': 18.86, 'Test_Qtypes/where is the': 20.38, 'Test_Qtypes/what room is': 73.45, 'Test_Atypes/other': 24.8, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 62.44, 'Test/topk_optimal': 62.94, 'Test/topk_not_optimal': 3.44, 'Test_Qtypes/is there': 63.63, 'Test_Qtypes/is the': 61.58, 'Test_Qtypes/is this a': 64.47, 'Test_Qtypes/is there a': 62.68, 'Test_Qtypes/are the': 60.99, 'Test_Qtypes/is this': 63.01, 'Test_Qtypes/was': 60.1, 'Test_Qtypes/is': 64.81, 'Test_Qtypes/is it': 65.3, 'Test_Qtypes/are these': 57.99, 'Test_Qtypes/are there': 60.56, 'Test_Qtypes/is this an': 65.1, 'Test_Qtypes/are': 63.14, 'Test_Qtypes/is that a': 61.15, 'Test_Qtypes/are there any': 60.0, 'Test_Atypes/yes_no': 64.95, 'Test_Atypes/other': 30.37, 'Test_Atypes/number': 24.0}
q_commonsense {'Test/overall': 64.89, 'Test/topk_optimal': 65.11, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 63.58, 'Test_Qtypes/does the': 66.18, 'Test_Qtypes/does this': 67.38, 'Test_Qtypes/can you': 59.24, 'Test_Qtypes/has': 61.14, 'Test_Qtypes/could': 62.09, 'Test_Qtypes/do you': 68.93, 'Test_Atypes/yes_no': 66.38, 'Test_Atypes/other': 7.41, 'Test_Atypes/number': 45.0}
q_count {'Test/overall': 33.2, 'Test/topk_optimal': 33.49, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.33, 'Test_Qtypes/how many people are': 34.84, 'Test_Qtypes/how many people are in': 43.37, 'Test_Qtypes/what number is': 6.13, 'Test_Atypes/number': 33.43, 'Test_Atypes/other': 5.45}
q_recognition {'Test/overall': 19.94, 'Test/topk_optimal': 22.84, 'Test/topk_not_optimal': 2.13, 'Test_Qtypes/what': 17.78, 'Test_Qtypes/what is the': 22.38, 'Test_Qtypes/what is on the': 13.2, 'Test_Qtypes/what is': 16.59, 'Test_Qtypes/what is this': 37.65, 'Test_Qtypes/what are the': 21.67, 'Test_Qtypes/what are': 24.88, 'Test_Qtypes/what is in the': 23.23, 'Test_Qtypes/who is': 21.76, 'Test_Qtypes/what is the name': 3.15, 'Test_Qtypes/which': 24.17, 'Test_Qtypes/what does the': 15.02, 'Test_Atypes/other': 20.52, 'Test_Atypes/number': 4.13, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 25.16, 'Test/topk_optimal': 28.6, 'Test/topk_not_optimal': 2.93, 'Test_Qtypes/where are the': 24.94, 'Test_Qtypes/where is the': 18.32, 'Test_Qtypes/what room is': 75.27, 'Test_Atypes/other': 25.2, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 64.83, 'Test/topk_optimal': 65.34, 'Test/topk_not_optimal': 4.92, 'Test_Qtypes/is there': 66.28, 'Test_Qtypes/is the': 65.64, 'Test_Qtypes/is this a': 65.33, 'Test_Qtypes/is there a': 62.16, 'Test_Qtypes/are the': 65.29, 'Test_Qtypes/is this': 63.95, 'Test_Qtypes/was': 61.22, 'Test_Qtypes/is': 63.88, 'Test_Qtypes/is it': 68.55, 'Test_Qtypes/are these': 65.72, 'Test_Qtypes/are there': 61.34, 'Test_Qtypes/is this an': 66.22, 'Test_Qtypes/are': 63.76, 'Test_Qtypes/is that a': 58.54, 'Test_Qtypes/are there any': 65.04, 'Test_Atypes/yes_no': 67.3, 'Test_Atypes/other': 33.57, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 65.34, 'Test/topk_optimal': 65.58, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 61.64, 'Test_Qtypes/does the': 69.08, 'Test_Qtypes/does this': 69.92, 'Test_Qtypes/can you': 60.22, 'Test_Qtypes/has': 53.9, 'Test_Qtypes/could': 69.55, 'Test_Qtypes/do you': 63.21, 'Test_Atypes/yes_no': 66.4, 'Test_Atypes/other': 28.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 31.17, 'Test/topk_optimal': 31.45, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 31.21, 'Test_Qtypes/how many people are': 36.22, 'Test_Qtypes/how many people are in': 35.47, 'Test_Qtypes/what number is': 3.23, 'Test_Atypes/number': 31.42, 'Test_Atypes/other': 1.36}
q_action {'Test/overall': 56.82, 'Test/topk_optimal': 58.27, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 41.29, 'Test_Qtypes/are they': 62.47, 'Test_Qtypes/is this person': 64.87, 'Test_Qtypes/is he': 66.5, 'Test_Qtypes/what is the woman': 39.18, 'Test_Qtypes/is the man': 64.97, 'Test_Qtypes/is this': 67.83, 'Test_Qtypes/is the person': 61.39, 'Test_Qtypes/is the woman': 71.41, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 57.78, 'Test_Qtypes/what is the person': 45.6, 'Test_Qtypes/are the': 68.33, 'Test_Atypes/other': 41.01, 'Test_Atypes/yes_no': 67.33, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 17.0, 'Test/topk_optimal': 19.5, 'Test/topk_not_optimal': 1.64, 'Test_Qtypes/what': 16.76, 'Test_Qtypes/what is the': 18.5, 'Test_Qtypes/what is on the': 10.04, 'Test_Qtypes/what is': 13.12, 'Test_Qtypes/what is this': 31.99, 'Test_Qtypes/what are the': 18.53, 'Test_Qtypes/what are': 20.06, 'Test_Qtypes/what is in the': 15.44, 'Test_Qtypes/who is': 19.17, 'Test_Qtypes/what is the name': 1.37, 'Test_Qtypes/which': 25.87, 'Test_Qtypes/what does the': 6.58, 'Test_Atypes/other': 17.39, 'Test_Atypes/number': 5.87, 'Test_Atypes/yes_no': 50.0}
q_location {'Test/overall': 23.49, 'Test/topk_optimal': 26.16, 'Test/topk_not_optimal': 6.22, 'Test_Qtypes/where are the': 21.9, 'Test_Qtypes/where is the': 16.28, 'Test_Qtypes/what room is': 80.18, 'Test_Atypes/other': 23.52, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 62.07, 'Test/topk_optimal': 62.55, 'Test/topk_not_optimal': 4.92, 'Test_Qtypes/is there': 64.81, 'Test_Qtypes/is the': 61.92, 'Test_Qtypes/is this a': 60.5, 'Test_Qtypes/is there a': 63.89, 'Test_Qtypes/are the': 61.22, 'Test_Qtypes/is this': 62.57, 'Test_Qtypes/was': 62.65, 'Test_Qtypes/is': 65.42, 'Test_Qtypes/is it': 62.98, 'Test_Qtypes/are these': 61.92, 'Test_Qtypes/are there': 61.5, 'Test_Qtypes/is this an': 61.22, 'Test_Qtypes/are': 62.77, 'Test_Qtypes/is that a': 49.79, 'Test_Qtypes/are there any': 58.22, 'Test_Atypes/yes_no': 64.93, 'Test_Atypes/other': 25.66, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 65.93, 'Test/topk_optimal': 66.09, 'Test/topk_not_optimal': 30.0, 'Test_Qtypes/do': 64.59, 'Test_Qtypes/does the': 65.69, 'Test_Qtypes/does this': 71.17, 'Test_Qtypes/can you': 66.52, 'Test_Qtypes/has': 59.92, 'Test_Qtypes/could': 69.7, 'Test_Qtypes/do you': 59.05, 'Test_Atypes/yes_no': 67.21, 'Test_Atypes/other': 17.78, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 33.22, 'Test/topk_optimal': 33.5, 'Test/topk_not_optimal': 2.5, 'Test_Qtypes/how many': 34.07, 'Test_Qtypes/how many people are': 31.3, 'Test_Qtypes/how many people are in': 36.0, 'Test_Qtypes/what number is': 5.65, 'Test_Atypes/number': 33.49, 'Test_Atypes/other': 1.36}
q_action {'Test/overall': 52.26, 'Test/topk_optimal': 53.6, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 35.63, 'Test_Qtypes/are they': 55.96, 'Test_Qtypes/is this person': 62.82, 'Test_Qtypes/is he': 66.34, 'Test_Qtypes/what is the woman': 29.38, 'Test_Qtypes/is the man': 61.81, 'Test_Qtypes/is this': 59.13, 'Test_Qtypes/is the person': 61.14, 'Test_Qtypes/is the woman': 69.9, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 68.89, 'Test_Qtypes/what is the person': 34.0, 'Test_Qtypes/are the': 62.78, 'Test_Atypes/other': 33.98, 'Test_Atypes/yes_no': 64.25, 'Test_Atypes/number': 50.0}
q_color {'Test/overall': 52.75, 'Test/topk_optimal': 53.11, 'Test/topk_not_optimal': 20.0, 'Test_Qtypes/what color is the': 54.25, 'Test_Qtypes/what color': 47.28, 'Test_Qtypes/what color are the': 47.01, 'Test_Qtypes/what is the color of the': 58.0, 'Test_Qtypes/what color is': 52.96, 'Test_Atypes/other': 52.75}
q_recognition {'Test/overall': 20.06, 'Test/topk_optimal': 22.93, 'Test/topk_not_optimal': 2.44, 'Test_Qtypes/what': 20.64, 'Test_Qtypes/what is the': 22.1, 'Test_Qtypes/what is on the': 11.6, 'Test_Qtypes/what is': 15.54, 'Test_Qtypes/what is this': 35.31, 'Test_Qtypes/what are the': 18.13, 'Test_Qtypes/what are': 27.73, 'Test_Qtypes/what is in the': 24.01, 'Test_Qtypes/who is': 20.46, 'Test_Qtypes/what is the name': 1.78, 'Test_Qtypes/which': 21.7, 'Test_Qtypes/what does the': 11.46, 'Test_Atypes/other': 20.55, 'Test_Atypes/number': 6.68, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 25.38, 'Test/topk_optimal': 28.64, 'Test/topk_not_optimal': 4.39, 'Test_Qtypes/where are the': 19.56, 'Test_Qtypes/where is the': 19.97, 'Test_Qtypes/what room is': 81.27, 'Test_Atypes/other': 25.43, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 62.35, 'Test/topk_optimal': 62.86, 'Test/topk_not_optimal': 1.97, 'Test_Qtypes/is there': 58.31, 'Test_Qtypes/is the': 62.21, 'Test_Qtypes/is this a': 64.73, 'Test_Qtypes/is there a': 63.59, 'Test_Qtypes/are the': 61.1, 'Test_Qtypes/is this': 63.79, 'Test_Qtypes/was': 57.35, 'Test_Qtypes/is': 61.19, 'Test_Qtypes/is it': 63.42, 'Test_Qtypes/are these': 61.42, 'Test_Qtypes/are there': 61.76, 'Test_Qtypes/is this an': 59.29, 'Test_Qtypes/are': 64.58, 'Test_Qtypes/is that a': 58.54, 'Test_Qtypes/are there any': 59.33, 'Test_Atypes/yes_no': 65.17, 'Test_Atypes/other': 26.43, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 63.53, 'Test/topk_optimal': 63.76, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 63.21, 'Test_Qtypes/does the': 61.74, 'Test_Qtypes/does this': 69.96, 'Test_Qtypes/can you': 60.33, 'Test_Qtypes/has': 61.38, 'Test_Qtypes/could': 61.79, 'Test_Qtypes/do you': 60.12, 'Test_Atypes/yes_no': 64.71, 'Test_Atypes/other': 21.48, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 31.65, 'Test/topk_optimal': 31.92, 'Test/topk_not_optimal': 2.5, 'Test_Qtypes/how many': 31.93, 'Test_Qtypes/how many people are': 32.68, 'Test_Qtypes/how many people are in': 38.53, 'Test_Qtypes/what number is': 6.94, 'Test_Atypes/number': 31.9, 'Test_Atypes/other': 1.36}
q_action {'Test/overall': 52.9, 'Test/topk_optimal': 54.28, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 35.98, 'Test_Qtypes/are they': 59.4, 'Test_Qtypes/is this person': 60.26, 'Test_Qtypes/is he': 63.9, 'Test_Qtypes/what is the woman': 35.77, 'Test_Qtypes/is the man': 62.01, 'Test_Qtypes/is this': 48.7, 'Test_Qtypes/is the person': 59.62, 'Test_Qtypes/is the woman': 66.16, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 62.22, 'Test_Qtypes/what is the person': 40.7, 'Test_Qtypes/are the': 71.11, 'Test_Atypes/other': 35.45, 'Test_Atypes/yes_no': 64.47, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 48.13, 'Test/topk_optimal': 48.53, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 49.62, 'Test_Qtypes/what color': 43.31, 'Test_Qtypes/what color are the': 43.2, 'Test_Qtypes/what is the color of the': 51.41, 'Test_Qtypes/what color is': 47.18, 'Test_Atypes/other': 48.13}
q_type {'Test/overall': 31.44, 'Test/topk_optimal': 34.02, 'Test/topk_not_optimal': 6.47, 'Test_Qtypes/what type of': 32.0, 'Test_Qtypes/what kind of': 31.05, 'Test_Atypes/other': 31.47, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 20.56, 'Test/topk_optimal': 23.54, 'Test/topk_not_optimal': 2.17, 'Test_Qtypes/what': 20.78, 'Test_Qtypes/what is the': 21.43, 'Test_Qtypes/what is on the': 14.16, 'Test_Qtypes/what is': 15.35, 'Test_Qtypes/what is this': 35.97, 'Test_Qtypes/what are the': 20.44, 'Test_Qtypes/what are': 25.81, 'Test_Qtypes/what is in the': 28.06, 'Test_Qtypes/who is': 22.59, 'Test_Qtypes/what is the name': 4.52, 'Test_Qtypes/which': 23.84, 'Test_Qtypes/what does the': 12.74, 'Test_Atypes/other': 21.17, 'Test_Atypes/number': 3.83, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 25.12, 'Test/topk_optimal': 28.62, 'Test/topk_not_optimal': 2.56, 'Test_Qtypes/where are the': 21.33, 'Test_Qtypes/where is the': 20.8, 'Test_Qtypes/what room is': 67.27, 'Test_Atypes/other': 25.16, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 62.87, 'Test/topk_optimal': 63.36, 'Test/topk_not_optimal': 4.92, 'Test_Qtypes/is there': 63.61, 'Test_Qtypes/is the': 61.36, 'Test_Qtypes/is this a': 63.81, 'Test_Qtypes/is there a': 59.8, 'Test_Qtypes/are the': 63.44, 'Test_Qtypes/is this': 63.14, 'Test_Qtypes/was': 53.78, 'Test_Qtypes/is': 64.84, 'Test_Qtypes/is it': 65.96, 'Test_Qtypes/are these': 63.96, 'Test_Qtypes/are there': 61.18, 'Test_Qtypes/is this an': 74.39, 'Test_Qtypes/are': 65.68, 'Test_Qtypes/is that a': 62.71, 'Test_Qtypes/are there any': 63.19, 'Test_Atypes/yes_no': 65.86, 'Test_Atypes/other': 24.81, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 64.62, 'Test/topk_optimal': 64.86, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 61.26, 'Test_Qtypes/does the': 65.05, 'Test_Qtypes/does this': 66.37, 'Test_Qtypes/can you': 67.61, 'Test_Qtypes/has': 63.5, 'Test_Qtypes/could': 62.39, 'Test_Qtypes/do you': 64.29, 'Test_Atypes/yes_no': 65.56, 'Test_Atypes/other': 32.22, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 29.23, 'Test/topk_optimal': 29.49, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 29.92, 'Test_Qtypes/how many people are': 29.59, 'Test_Qtypes/how many people are in': 29.26, 'Test_Qtypes/what number is': 2.58, 'Test_Atypes/number': 29.47, 'Test_Atypes/other': 1.36}
q_action {'Test/overall': 53.45, 'Test/topk_optimal': 54.72, 'Test/topk_not_optimal': 5.14, 'Test_Qtypes/what is the man': 33.22, 'Test_Qtypes/are they': 63.07, 'Test_Qtypes/is this person': 67.69, 'Test_Qtypes/is he': 66.1, 'Test_Qtypes/what is the woman': 29.38, 'Test_Qtypes/is the man': 63.54, 'Test_Qtypes/is this': 36.96, 'Test_Qtypes/is the person': 61.39, 'Test_Qtypes/is the woman': 67.17, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 76.67, 'Test_Qtypes/what is the person': 42.6, 'Test_Qtypes/are the': 70.0, 'Test_Atypes/other': 33.72, 'Test_Atypes/yes_no': 66.52, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 45.36, 'Test/topk_optimal': 45.67, 'Test/topk_not_optimal': 17.5, 'Test_Qtypes/what color is the': 45.98, 'Test_Qtypes/what color': 39.21, 'Test_Qtypes/what color are the': 41.83, 'Test_Qtypes/what is the color of the': 55.29, 'Test_Qtypes/what color is': 47.61, 'Test_Atypes/other': 45.36}
q_type {'Test/overall': 25.47, 'Test/topk_optimal': 27.83, 'Test/topk_not_optimal': 2.65, 'Test_Qtypes/what type of': 26.47, 'Test_Qtypes/what kind of': 24.76, 'Test_Atypes/other': 25.5, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 47.8, 'Test/topk_optimal': 52.17, 'Test/topk_not_optimal': 3.1, 'Test_Qtypes/none of the above': 45.72, 'Test_Qtypes/what time': 26.23, 'Test_Qtypes/what sport is': 77.38, 'Test_Qtypes/what brand': 33.46, 'Test_Qtypes/what animal is': 69.47, 'Test_Atypes/number': 5.17, 'Test_Atypes/other': 41.59, 'Test_Atypes/yes_no': 67.27}
q_recognition {'Test/overall': 20.78, 'Test/topk_optimal': 23.79, 'Test/topk_not_optimal': 2.25, 'Test_Qtypes/what': 20.72, 'Test_Qtypes/what is the': 22.09, 'Test_Qtypes/what is on the': 10.91, 'Test_Qtypes/what is': 14.85, 'Test_Qtypes/what is this': 39.95, 'Test_Qtypes/what are the': 24.08, 'Test_Qtypes/what are': 27.91, 'Test_Qtypes/what is in the': 25.53, 'Test_Qtypes/who is': 19.44, 'Test_Qtypes/what is the name': 3.15, 'Test_Qtypes/which': 24.54, 'Test_Qtypes/what does the': 12.01, 'Test_Atypes/other': 21.41, 'Test_Atypes/number': 3.11, 'Test_Atypes/yes_no': 50.0}
q_location {'Test/overall': 27.14, 'Test/topk_optimal': 31.0, 'Test/topk_not_optimal': 2.2, 'Test_Qtypes/where are the': 26.46, 'Test_Qtypes/where is the': 20.33, 'Test_Qtypes/what room is': 78.36, 'Test_Atypes/other': 27.18, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 62.54, 'Test/topk_optimal': 63.03, 'Test/topk_not_optimal': 4.92, 'Test_Qtypes/is there': 61.23, 'Test_Qtypes/is the': 63.02, 'Test_Qtypes/is this a': 62.43, 'Test_Qtypes/is there a': 61.19, 'Test_Qtypes/are the': 61.24, 'Test_Qtypes/is this': 62.94, 'Test_Qtypes/was': 58.27, 'Test_Qtypes/is': 63.48, 'Test_Qtypes/is it': 64.53, 'Test_Qtypes/are these': 63.74, 'Test_Qtypes/are there': 59.28, 'Test_Qtypes/is this an': 65.1, 'Test_Qtypes/are': 64.69, 'Test_Qtypes/is that a': 66.25, 'Test_Qtypes/are there any': 58.59, 'Test_Atypes/yes_no': 65.4, 'Test_Atypes/other': 26.09, 'Test_Atypes/number': 12.0}
q_commonsense {'Test/overall': 65.67, 'Test/topk_optimal': 65.97, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 67.92, 'Test_Qtypes/does the': 63.3, 'Test_Qtypes/does this': 70.12, 'Test_Qtypes/can you': 66.09, 'Test_Qtypes/has': 58.37, 'Test_Qtypes/could': 65.82, 'Test_Qtypes/do you': 67.62, 'Test_Atypes/yes_no': 66.91, 'Test_Atypes/other': 21.48, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 29.7, 'Test/topk_optimal': 29.94, 'Test/topk_not_optimal': 2.5, 'Test_Qtypes/how many': 30.59, 'Test_Qtypes/how many people are': 26.83, 'Test_Qtypes/how many people are in': 34.95, 'Test_Qtypes/what number is': 0.48, 'Test_Atypes/number': 29.93, 'Test_Atypes/other': 1.36}
q_action {'Test/overall': 52.97, 'Test/topk_optimal': 54.27, 'Test/topk_not_optimal': 3.43, 'Test_Qtypes/what is the man': 34.48, 'Test_Qtypes/are they': 61.75, 'Test_Qtypes/is this person': 69.87, 'Test_Qtypes/is he': 64.88, 'Test_Qtypes/what is the woman': 34.43, 'Test_Qtypes/is the man': 60.21, 'Test_Qtypes/is this': 41.74, 'Test_Qtypes/is the person': 60.25, 'Test_Qtypes/is the woman': 64.65, 'Test_Qtypes/are': 28.57, 'Test_Qtypes/are these': 70.0, 'Test_Qtypes/what is the person': 46.5, 'Test_Qtypes/are the': 50.56, 'Test_Atypes/other': 35.29, 'Test_Atypes/yes_no': 64.7, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 43.27, 'Test/topk_optimal': 43.57, 'Test/topk_not_optimal': 16.25, 'Test_Qtypes/what color is the': 44.29, 'Test_Qtypes/what color': 41.26, 'Test_Qtypes/what color are the': 37.71, 'Test_Qtypes/what is the color of the': 42.94, 'Test_Qtypes/what color is': 47.75, 'Test_Atypes/other': 43.27}
q_type {'Test/overall': 26.52, 'Test/topk_optimal': 28.8, 'Test/topk_not_optimal': 4.41, 'Test_Qtypes/what type of': 28.56, 'Test_Qtypes/what kind of': 25.08, 'Test_Atypes/other': 26.54, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 48.48, 'Test/topk_optimal': 53.05, 'Test/topk_not_optimal': 1.67, 'Test_Qtypes/none of the above': 47.87, 'Test_Qtypes/what time': 20.62, 'Test_Qtypes/what sport is': 76.0, 'Test_Qtypes/what brand': 36.54, 'Test_Qtypes/what animal is': 65.22, 'Test_Atypes/number': 3.68, 'Test_Atypes/other': 39.72, 'Test_Atypes/yes_no': 73.07}
q_causal {'Test/overall': 11.2, 'Test/topk_optimal': 13.48, 'Test/topk_not_optimal': 3.33, 'Test_Qtypes/why': 11.66, 'Test_Qtypes/why is the': 9.8, 'Test_Atypes/other': 11.26, 'Test_Atypes/number': 0.0}
#------------------ result_matrix --------------------#
q_recognition   q_location      q_judge q_commonsense   q_count q_action        q_color q_type  q_subcategory   q_causal

27.19   0.0     0.0     0.0     0.0     0.0     0.0     0.0     0.0     0.0     Avg: 2.72
25.02   30.39   0.0     0.0     0.0     0.0     0.0     0.0     0.0     0.0     Avg: 5.54
15.81   23.18   63.35   0.0     0.0     0.0     0.0     0.0     0.0     0.0     Avg: 10.23
19.15   26.43   65.44   66.01   0.0     0.0     0.0     0.0     0.0     0.0     Avg: 17.7
15.67   24.76   62.44   64.89   33.2    0.0     0.0     0.0     0.0     0.0     Avg: 20.1
19.94   25.16   64.83   65.34   31.17   56.82   0.0     0.0     0.0     0.0     Avg: 26.33
17.0    23.49   62.07   65.93   33.22   52.26   52.75   0.0     0.0     0.0     Avg: 30.67
20.06   25.38   62.35   63.53   31.65   52.9    48.13   31.44   0.0     0.0     Avg: 33.54
20.56   25.12   62.87   64.62   29.23   53.45   45.36   25.47   47.8    0.0     Avg: 37.45
20.78   27.14   62.54   65.67   29.7    52.97   43.27   26.52   48.48   11.2    Avg: 38.83
#------  Metric  ------#
Incremental avg accuracy: [np.float64(27.19), np.float64(27.705), np.float64(34.11333333333334), np.float64(44.2575), np.float64(40.19199999999999), np.float64(43.876666666666665), np.float64(43.81714285714286), np.float64(41.93), np.float64(41.6088888888889), np.float64(38.82699999999999)]
*** Avg accuracy *** 38.82699999999999
Incremental avg forget: [0, np.float64(2.1700000000000017), np.float64(9.295000000000002), np.float64(3.303333333333336), np.float64(5.317500000000001), np.float64(3.1580000000000004), np.float64(4.180000000000001), np.float64(3.9742857142857146), np.float64(4.5725), np.float64(3.776666666666667)]
*** Avg forget *** 3.776666666666667
6Q Incremental avg accuracy: [-1, np.float64(30.39), np.float64(23.18), np.float64(26.43), np.float64(28.980000000000004), np.float64(37.71666666666667), np.float64(40.43), np.float64(37.9), np.float64(37.73833333333334), np.float64(38.013333333333335)]
*** _6Q Avg accuracy *** 38.013333333333335
_6Q Incremental avg forget: [0, np.float64(2.1700000000000017), np.float64(11.38), np.float64(8.040000000000003), np.float64(6.320000000000003), np.float64(3.3166666666666678), np.float64(3.7025000000000015), np.float64(3.9440000000000013), np.float64(4.79), np.float64(4.753333333333334)]
*** _6Q Avg forget *** 4.753333333333334


q_location {'Test/overall': 16.87, 'Test/topk_optimal': 18.87, 'Test/topk_not_optimal': 4.02, 'Test_Qtypes/where are the': 13.16, 'Test_Qtypes/where is the': 11.63, 'Test_Qtypes/what room is': 65.45, 'Test_Atypes/other': 16.9, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 26.35, 'Test/topk_optimal': 29.98, 'Test/topk_not_optimal': 2.93, 'Test_Qtypes/where are the': 22.97, 'Test_Qtypes/where is the': 20.08, 'Test_Qtypes/what room is': 81.45, 'Test_Atypes/other': 26.39, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 24.57, 'Test/topk_optimal': 28.15, 'Test/topk_not_optimal': 2.55, 'Test_Qtypes/what': 24.54, 'Test_Qtypes/what is the': 24.86, 'Test_Qtypes/what is on the': 24.29, 'Test_Qtypes/what is': 20.74, 'Test_Qtypes/what is this': 34.23, 'Test_Qtypes/what are the': 25.9, 'Test_Qtypes/what are': 29.59, 'Test_Qtypes/what is in the': 29.03, 'Test_Qtypes/who is': 19.35, 'Test_Qtypes/what is the name': 2.19, 'Test_Qtypes/which': 31.33, 'Test_Qtypes/what does the': 18.17, 'Test_Atypes/other': 25.28, 'Test_Atypes/number': 5.05, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 28.58, 'Test/topk_optimal': 32.27, 'Test/topk_not_optimal': 4.76, 'Test_Qtypes/where are the': 25.95, 'Test_Qtypes/where is the': 23.27, 'Test_Qtypes/what room is': 74.55, 'Test_Atypes/other': 28.62, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 17.95, 'Test/topk_optimal': 20.51, 'Test/topk_not_optimal': 2.21, 'Test_Qtypes/what': 17.63, 'Test_Qtypes/what is the': 19.68, 'Test_Qtypes/what is on the': 13.98, 'Test_Qtypes/what is': 13.95, 'Test_Qtypes/what is this': 29.59, 'Test_Qtypes/what are the': 20.47, 'Test_Qtypes/what are': 20.29, 'Test_Qtypes/what is in the': 21.57, 'Test_Qtypes/who is': 15.74, 'Test_Qtypes/what is the name': 3.01, 'Test_Qtypes/which': 23.25, 'Test_Qtypes/what does the': 7.03, 'Test_Atypes/other': 18.43, 'Test_Atypes/number': 4.8, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 34.51, 'Test/topk_optimal': 34.82, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 34.45, 'Test_Qtypes/how many people are': 40.53, 'Test_Qtypes/how many people are in': 39.68, 'Test_Qtypes/what number is': 4.84, 'Test_Atypes/number': 34.77, 'Test_Atypes/other': 2.73}
q_location {'Test/overall': 26.09, 'Test/topk_optimal': 29.0, 'Test/topk_not_optimal': 7.32, 'Test_Qtypes/where are the': 21.71, 'Test_Qtypes/where is the': 19.67, 'Test_Qtypes/what room is': 85.09, 'Test_Atypes/other': 26.13, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 19.81, 'Test/topk_optimal': 22.73, 'Test/topk_not_optimal': 1.83, 'Test_Qtypes/what': 19.6, 'Test_Qtypes/what is the': 22.85, 'Test_Qtypes/what is on the': 12.81, 'Test_Qtypes/what is': 14.77, 'Test_Qtypes/what is this': 27.3, 'Test_Qtypes/what are the': 19.68, 'Test_Qtypes/what are': 24.42, 'Test_Qtypes/what is in the': 23.55, 'Test_Qtypes/who is': 18.61, 'Test_Qtypes/what is the name': 4.11, 'Test_Qtypes/which': 28.19, 'Test_Qtypes/what does the': 9.73, 'Test_Atypes/other': 20.44, 'Test_Atypes/number': 2.6, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 31.81, 'Test/topk_optimal': 32.1, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 32.16, 'Test_Qtypes/how many people are': 33.21, 'Test_Qtypes/how many people are in': 38.74, 'Test_Qtypes/what number is': 3.23, 'Test_Atypes/number': 32.04, 'Test_Atypes/other': 4.09}
q_color {'Test/overall': 56.78, 'Test/topk_optimal': 57.21, 'Test/topk_not_optimal': 17.5, 'Test_Qtypes/what color is the': 58.12, 'Test_Qtypes/what color': 55.7, 'Test_Qtypes/what color are the': 48.84, 'Test_Qtypes/what is the color of the': 59.65, 'Test_Qtypes/what color is': 60.49, 'Test_Atypes/other': 56.78}
q_location {'Test/overall': 29.89, 'Test/topk_optimal': 33.67, 'Test/topk_not_optimal': 5.49, 'Test_Qtypes/where are the': 29.68, 'Test_Qtypes/where is the': 21.26, 'Test_Qtypes/what room is': 92.91, 'Test_Atypes/other': 29.93, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 21.81, 'Test/topk_optimal': 25.02, 'Test/topk_not_optimal': 2.1, 'Test_Qtypes/what': 23.82, 'Test_Qtypes/what is the': 22.96, 'Test_Qtypes/what is on the': 13.2, 'Test_Qtypes/what is': 15.47, 'Test_Qtypes/what is this': 33.88, 'Test_Qtypes/what are the': 21.79, 'Test_Qtypes/what are': 27.73, 'Test_Qtypes/what is in the': 24.24, 'Test_Qtypes/who is': 12.87, 'Test_Qtypes/what is the name': 3.97, 'Test_Qtypes/which': 31.73, 'Test_Qtypes/what does the': 9.27, 'Test_Atypes/other': 22.49, 'Test_Atypes/number': 3.37, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 30.82, 'Test/topk_optimal': 31.1, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 31.19, 'Test_Qtypes/how many people are': 31.3, 'Test_Qtypes/how many people are in': 40.63, 'Test_Qtypes/what number is': 0.48, 'Test_Atypes/number': 31.07, 'Test_Atypes/other': 1.36}
q_color {'Test/overall': 50.83, 'Test/topk_optimal': 51.18, 'Test/topk_not_optimal': 20.0, 'Test_Qtypes/what color is the': 51.32, 'Test_Qtypes/what color': 50.4, 'Test_Qtypes/what color are the': 47.65, 'Test_Qtypes/what is the color of the': 57.06, 'Test_Qtypes/what color is': 49.86, 'Test_Atypes/other': 50.83}
q_type {'Test/overall': 35.28, 'Test/topk_optimal': 38.35, 'Test/topk_not_optimal': 5.59, 'Test_Qtypes/what type of': 37.34, 'Test_Qtypes/what kind of': 33.82, 'Test_Atypes/other': 35.31, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 29.38, 'Test/topk_optimal': 33.25, 'Test/topk_not_optimal': 4.39, 'Test_Qtypes/where are the': 23.48, 'Test_Qtypes/where is the': 23.89, 'Test_Qtypes/what room is': 86.0, 'Test_Atypes/other': 29.43, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 22.14, 'Test/topk_optimal': 25.41, 'Test/topk_not_optimal': 2.06, 'Test_Qtypes/what': 24.14, 'Test_Qtypes/what is the': 23.95, 'Test_Qtypes/what is on the': 18.18, 'Test_Qtypes/what is': 15.48, 'Test_Qtypes/what is this': 33.16, 'Test_Qtypes/what are the': 22.16, 'Test_Qtypes/what are': 24.42, 'Test_Qtypes/what is in the': 27.37, 'Test_Qtypes/who is': 17.31, 'Test_Qtypes/what is the name': 3.15, 'Test_Qtypes/which': 22.29, 'Test_Qtypes/what does the': 13.47, 'Test_Atypes/other': 22.83, 'Test_Atypes/number': 3.32, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 32.11, 'Test/topk_optimal': 32.4, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 32.86, 'Test_Qtypes/how many people are': 29.02, 'Test_Qtypes/how many people are in': 40.21, 'Test_Qtypes/what number is': 4.84, 'Test_Atypes/number': 32.38, 'Test_Atypes/other': 0.0}
q_color {'Test/overall': 51.74, 'Test/topk_optimal': 52.09, 'Test/topk_not_optimal': 20.0, 'Test_Qtypes/what color is the': 52.93, 'Test_Qtypes/what color': 48.08, 'Test_Qtypes/what color are the': 45.61, 'Test_Qtypes/what is the color of the': 55.18, 'Test_Qtypes/what color is': 55.21, 'Test_Atypes/other': 51.74}
q_type {'Test/overall': 31.67, 'Test/topk_optimal': 34.43, 'Test/topk_not_optimal': 5.0, 'Test_Qtypes/what type of': 30.82, 'Test_Qtypes/what kind of': 32.27, 'Test_Atypes/other': 31.7, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 45.71, 'Test/topk_optimal': 49.67, 'Test/topk_not_optimal': 5.24, 'Test_Qtypes/none of the above': 46.4, 'Test_Qtypes/what time': 14.92, 'Test_Qtypes/what sport is': 80.0, 'Test_Qtypes/what brand': 28.32, 'Test_Qtypes/what animal is': 52.48, 'Test_Atypes/number': 7.7, 'Test_Atypes/other': 37.06, 'Test_Atypes/yes_no': 68.84}
q_location {'Test/overall': 23.6, 'Test/topk_optimal': 26.47, 'Test/topk_not_optimal': 5.12, 'Test_Qtypes/where are the': 18.42, 'Test_Qtypes/where is the': 17.84, 'Test_Qtypes/what room is': 80.18, 'Test_Atypes/other': 23.64, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 15.88, 'Test/topk_optimal': 18.17, 'Test/topk_not_optimal': 1.83, 'Test_Qtypes/what': 16.87, 'Test_Qtypes/what is the': 16.81, 'Test_Qtypes/what is on the': 10.39, 'Test_Qtypes/what is': 12.24, 'Test_Qtypes/what is this': 24.34, 'Test_Qtypes/what are the': 17.57, 'Test_Qtypes/what are': 19.3, 'Test_Qtypes/what is in the': 12.58, 'Test_Qtypes/who is': 12.87, 'Test_Qtypes/what is the name': 4.93, 'Test_Qtypes/which': 18.15, 'Test_Qtypes/what does the': 12.69, 'Test_Atypes/other': 16.29, 'Test_Atypes/number': 4.85, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 28.91, 'Test/topk_optimal': 29.16, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 29.31, 'Test_Qtypes/how many people are': 27.56, 'Test_Qtypes/how many people are in': 37.16, 'Test_Qtypes/what number is': 7.26, 'Test_Atypes/number': 29.09, 'Test_Atypes/other': 8.18}
q_color {'Test/overall': 41.96, 'Test/topk_optimal': 42.24, 'Test/topk_not_optimal': 16.25, 'Test_Qtypes/what color is the': 41.42, 'Test_Qtypes/what color': 45.1, 'Test_Qtypes/what color are the': 40.3, 'Test_Qtypes/what is the color of the': 44.24, 'Test_Qtypes/what color is': 46.69, 'Test_Atypes/other': 41.96}
q_type {'Test/overall': 20.97, 'Test/topk_optimal': 22.72, 'Test/topk_not_optimal': 4.12, 'Test_Qtypes/what type of': 20.53, 'Test_Qtypes/what kind of': 21.29, 'Test_Atypes/other': 20.97, 'Test_Atypes/number': 30.0}
q_subcategory {'Test/overall': 42.15, 'Test/topk_optimal': 46.05, 'Test/topk_not_optimal': 2.14, 'Test_Qtypes/none of the above': 40.88, 'Test_Qtypes/what time': 20.15, 'Test_Qtypes/what sport is': 73.15, 'Test_Qtypes/what brand': 23.27, 'Test_Qtypes/what animal is': 60.18, 'Test_Atypes/number': 3.22, 'Test_Atypes/other': 32.6, 'Test_Atypes/yes_no': 67.1}
q_judge {'Test/overall': 65.55, 'Test/topk_optimal': 66.09, 'Test/topk_not_optimal': 3.44, 'Test_Qtypes/is there': 65.68, 'Test_Qtypes/is the': 65.3, 'Test_Qtypes/is this a': 67.88, 'Test_Qtypes/is there a': 65.54, 'Test_Qtypes/are the': 61.52, 'Test_Qtypes/is this': 67.18, 'Test_Qtypes/was': 64.08, 'Test_Qtypes/is': 65.39, 'Test_Qtypes/is it': 71.4, 'Test_Qtypes/are these': 63.27, 'Test_Qtypes/are there': 60.82, 'Test_Qtypes/is this an': 69.29, 'Test_Qtypes/are': 64.46, 'Test_Qtypes/is that a': 60.31, 'Test_Qtypes/are there any': 65.04, 'Test_Atypes/yes_no': 67.31, 'Test_Atypes/other': 43.14, 'Test_Atypes/number': 38.0}
q_location {'Test/overall': 22.88, 'Test/topk_optimal': 25.63, 'Test/topk_not_optimal': 5.12, 'Test_Qtypes/where are the': 18.16, 'Test_Qtypes/where is the': 17.16, 'Test_Qtypes/what room is': 77.82, 'Test_Atypes/other': 22.87, 'Test_Atypes/number': 30.0}
q_recognition {'Test/overall': 17.49, 'Test/topk_optimal': 19.98, 'Test/topk_not_optimal': 2.17, 'Test_Qtypes/what': 19.69, 'Test_Qtypes/what is the': 17.24, 'Test_Qtypes/what is on the': 13.2, 'Test_Qtypes/what is': 12.17, 'Test_Qtypes/what is this': 26.28, 'Test_Qtypes/what are the': 16.76, 'Test_Qtypes/what are': 18.31, 'Test_Qtypes/what is in the': 19.63, 'Test_Qtypes/who is': 19.44, 'Test_Qtypes/what is the name': 1.37, 'Test_Qtypes/which': 21.37, 'Test_Qtypes/what does the': 13.38, 'Test_Atypes/other': 17.98, 'Test_Atypes/number': 3.98, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 31.7, 'Test/topk_optimal': 31.99, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 32.46, 'Test_Qtypes/how many people are': 27.4, 'Test_Qtypes/how many people are in': 42.42, 'Test_Qtypes/what number is': 4.84, 'Test_Atypes/number': 31.97, 'Test_Atypes/other': 0.0}
q_color {'Test/overall': 44.54, 'Test/topk_optimal': 44.92, 'Test/topk_not_optimal': 10.0, 'Test_Qtypes/what color is the': 44.8, 'Test_Qtypes/what color': 45.1, 'Test_Qtypes/what color are the': 39.88, 'Test_Qtypes/what is the color of the': 54.47, 'Test_Qtypes/what color is': 45.99, 'Test_Atypes/other': 44.54}
q_type {'Test/overall': 23.31, 'Test/topk_optimal': 25.32, 'Test/topk_not_optimal': 3.82, 'Test_Qtypes/what type of': 23.95, 'Test_Qtypes/what kind of': 22.85, 'Test_Atypes/other': 23.33, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 42.08, 'Test/topk_optimal': 45.87, 'Test/topk_not_optimal': 3.33, 'Test_Qtypes/none of the above': 42.54, 'Test_Qtypes/what time': 23.46, 'Test_Qtypes/what sport is': 68.62, 'Test_Qtypes/what brand': 28.97, 'Test_Qtypes/what animal is': 41.59, 'Test_Atypes/number': 5.17, 'Test_Atypes/other': 31.61, 'Test_Atypes/yes_no': 68.37}
q_judge {'Test/overall': 64.3, 'Test/topk_optimal': 64.81, 'Test/topk_not_optimal': 4.43, 'Test_Qtypes/is there': 62.35, 'Test_Qtypes/is the': 63.4, 'Test_Qtypes/is this a': 66.61, 'Test_Qtypes/is there a': 60.73, 'Test_Qtypes/are the': 65.34, 'Test_Qtypes/is this': 64.8, 'Test_Qtypes/was': 64.9, 'Test_Qtypes/is': 66.52, 'Test_Qtypes/is it': 68.4, 'Test_Qtypes/are these': 67.86, 'Test_Qtypes/are there': 57.75, 'Test_Qtypes/is this an': 65.0, 'Test_Qtypes/are': 64.28, 'Test_Qtypes/is that a': 62.4, 'Test_Qtypes/are there any': 61.85, 'Test_Atypes/yes_no': 66.72, 'Test_Atypes/other': 33.14, 'Test_Atypes/number': 40.0}
q_commonsense {'Test/overall': 63.41, 'Test/topk_optimal': 63.64, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 59.31, 'Test_Qtypes/does the': 65.93, 'Test_Qtypes/does this': 62.38, 'Test_Qtypes/can you': 61.74, 'Test_Qtypes/has': 58.94, 'Test_Qtypes/could': 75.22, 'Test_Qtypes/do you': 63.33, 'Test_Atypes/yes_no': 64.4, 'Test_Atypes/other': 28.89, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 25.7, 'Test/topk_optimal': 29.05, 'Test/topk_not_optimal': 4.02, 'Test_Qtypes/where are the': 20.38, 'Test_Qtypes/where is the': 19.2, 'Test_Qtypes/what room is': 88.0, 'Test_Atypes/other': 25.74, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 20.53, 'Test/topk_optimal': 23.51, 'Test/topk_not_optimal': 2.25, 'Test_Qtypes/what': 19.66, 'Test_Qtypes/what is the': 21.03, 'Test_Qtypes/what is on the': 15.63, 'Test_Qtypes/what is': 18.16, 'Test_Qtypes/what is this': 37.24, 'Test_Qtypes/what are the': 21.67, 'Test_Qtypes/what are': 29.36, 'Test_Qtypes/what is in the': 19.59, 'Test_Qtypes/who is': 19.91, 'Test_Qtypes/what is the name': 2.19, 'Test_Qtypes/which': 25.06, 'Test_Qtypes/what does the': 15.57, 'Test_Atypes/other': 21.11, 'Test_Atypes/number': 4.8, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 30.0, 'Test/topk_optimal': 30.27, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 31.13, 'Test_Qtypes/how many people are': 26.02, 'Test_Qtypes/how many people are in': 32.0, 'Test_Qtypes/what number is': 1.61, 'Test_Atypes/number': 30.23, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 45.64, 'Test/topk_optimal': 45.98, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 45.27, 'Test_Qtypes/what color': 48.87, 'Test_Qtypes/what color are the': 41.31, 'Test_Qtypes/what is the color of the': 58.12, 'Test_Qtypes/what color is': 48.59, 'Test_Atypes/other': 45.64}
q_type {'Test/overall': 24.07, 'Test/topk_optimal': 25.89, 'Test/topk_not_optimal': 6.47, 'Test_Qtypes/what type of': 24.75, 'Test_Qtypes/what kind of': 23.59, 'Test_Atypes/other': 24.09, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 42.9, 'Test/topk_optimal': 46.88, 'Test/topk_not_optimal': 2.14, 'Test_Qtypes/none of the above': 44.0, 'Test_Qtypes/what time': 22.23, 'Test_Qtypes/what sport is': 74.08, 'Test_Qtypes/what brand': 17.1, 'Test_Qtypes/what animal is': 46.11, 'Test_Atypes/number': 3.1, 'Test_Atypes/other': 31.88, 'Test_Atypes/yes_no': 70.73}
q_judge {'Test/overall': 65.33, 'Test/topk_optimal': 65.88, 'Test/topk_not_optimal': 1.48, 'Test_Qtypes/is there': 65.19, 'Test_Qtypes/is the': 64.06, 'Test_Qtypes/is this a': 68.24, 'Test_Qtypes/is there a': 65.06, 'Test_Qtypes/are the': 64.15, 'Test_Qtypes/is this': 66.12, 'Test_Qtypes/was': 57.45, 'Test_Qtypes/is': 67.97, 'Test_Qtypes/is it': 69.06, 'Test_Qtypes/are these': 68.58, 'Test_Qtypes/are there': 65.13, 'Test_Qtypes/is this an': 60.51, 'Test_Qtypes/are': 62.73, 'Test_Qtypes/is that a': 57.92, 'Test_Qtypes/are there any': 61.41, 'Test_Atypes/yes_no': 67.5, 'Test_Atypes/other': 37.81, 'Test_Atypes/number': 20.0}
q_commonsense {'Test/overall': 65.45, 'Test/topk_optimal': 65.69, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 66.23, 'Test_Qtypes/does the': 64.65, 'Test_Qtypes/does this': 68.63, 'Test_Qtypes/can you': 64.46, 'Test_Qtypes/has': 54.8, 'Test_Qtypes/could': 70.75, 'Test_Qtypes/do you': 70.12, 'Test_Atypes/yes_no': 66.61, 'Test_Atypes/other': 24.07, 'Test_Atypes/number': 0.0}
q_action {'Test/overall': 57.99, 'Test/topk_optimal': 59.37, 'Test/topk_not_optimal': 5.14, 'Test_Qtypes/what is the man': 42.83, 'Test_Qtypes/are they': 59.34, 'Test_Qtypes/is this person': 70.51, 'Test_Qtypes/is he': 65.93, 'Test_Qtypes/what is the woman': 40.72, 'Test_Qtypes/is the man': 69.48, 'Test_Qtypes/is this': 74.78, 'Test_Qtypes/is the person': 70.38, 'Test_Qtypes/is the woman': 64.24, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 57.78, 'Test_Qtypes/what is the person': 44.0, 'Test_Qtypes/are the': 60.56, 'Test_Atypes/other': 42.5, 'Test_Atypes/yes_no': 68.29, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 22.83, 'Test/topk_optimal': 25.63, 'Test/topk_not_optimal': 4.76, 'Test_Qtypes/where are the': 18.54, 'Test_Qtypes/where is the': 16.78, 'Test_Qtypes/what room is': 78.91, 'Test_Atypes/other': 22.87, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 19.82, 'Test/topk_optimal': 22.65, 'Test/topk_not_optimal': 2.4, 'Test_Qtypes/what': 20.17, 'Test_Qtypes/what is the': 21.44, 'Test_Qtypes/what is on the': 16.41, 'Test_Qtypes/what is': 15.67, 'Test_Qtypes/what is this': 36.38, 'Test_Qtypes/what are the': 23.29, 'Test_Qtypes/what are': 28.66, 'Test_Qtypes/what is in the': 18.16, 'Test_Qtypes/who is': 11.11, 'Test_Qtypes/what is the name': 3.56, 'Test_Qtypes/which': 16.35, 'Test_Qtypes/what does the': 12.47, 'Test_Atypes/other': 20.35, 'Test_Atypes/number': 5.31, 'Test_Atypes/yes_no': 0.0}
q_count {'Test/overall': 30.66, 'Test/topk_optimal': 30.94, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 31.45, 'Test_Qtypes/how many people are': 27.6, 'Test_Qtypes/how many people are in': 37.79, 'Test_Qtypes/what number is': 3.06, 'Test_Atypes/number': 30.89, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 44.24, 'Test/topk_optimal': 44.58, 'Test/topk_not_optimal': 13.75, 'Test_Qtypes/what color is the': 44.9, 'Test_Qtypes/what color': 40.4, 'Test_Qtypes/what color are the': 41.25, 'Test_Qtypes/what is the color of the': 52.94, 'Test_Qtypes/what color is': 43.1, 'Test_Atypes/other': 44.24}
q_type {'Test/overall': 28.25, 'Test/topk_optimal': 30.95, 'Test/topk_not_optimal': 2.06, 'Test_Qtypes/what type of': 27.1, 'Test_Qtypes/what kind of': 29.06, 'Test_Atypes/other': 28.27, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 42.82, 'Test/topk_optimal': 46.64, 'Test/topk_not_optimal': 3.81, 'Test_Qtypes/none of the above': 42.2, 'Test_Qtypes/what time': 21.08, 'Test_Qtypes/what sport is': 67.54, 'Test_Qtypes/what brand': 23.46, 'Test_Qtypes/what animal is': 62.92, 'Test_Atypes/number': 5.29, 'Test_Atypes/other': 31.53, 'Test_Atypes/yes_no': 70.75}
q_judge {'Test/overall': 64.13, 'Test/topk_optimal': 64.67, 'Test/topk_not_optimal': 1.48, 'Test_Qtypes/is there': 64.67, 'Test_Qtypes/is the': 63.67, 'Test_Qtypes/is this a': 65.3, 'Test_Qtypes/is there a': 62.68, 'Test_Qtypes/are the': 61.83, 'Test_Qtypes/is this': 65.19, 'Test_Qtypes/was': 62.45, 'Test_Qtypes/is': 66.12, 'Test_Qtypes/is it': 68.37, 'Test_Qtypes/are these': 62.42, 'Test_Qtypes/are there': 68.43, 'Test_Qtypes/is this an': 65.2, 'Test_Qtypes/are': 61.25, 'Test_Qtypes/is that a': 56.87, 'Test_Qtypes/are there any': 58.59, 'Test_Atypes/yes_no': 66.43, 'Test_Atypes/other': 34.59, 'Test_Atypes/number': 44.0}
q_commonsense {'Test/overall': 66.5, 'Test/topk_optimal': 66.75, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 60.94, 'Test_Qtypes/does the': 67.31, 'Test_Qtypes/does this': 73.67, 'Test_Qtypes/can you': 62.39, 'Test_Qtypes/has': 56.75, 'Test_Qtypes/could': 67.61, 'Test_Qtypes/do you': 70.6, 'Test_Atypes/yes_no': 67.76, 'Test_Atypes/other': 21.48, 'Test_Atypes/number': 0.0}
q_action {'Test/overall': 57.2, 'Test/topk_optimal': 58.57, 'Test/topk_not_optimal': 5.14, 'Test_Qtypes/what is the man': 37.87, 'Test_Qtypes/are they': 64.46, 'Test_Qtypes/is this person': 71.03, 'Test_Qtypes/is he': 68.29, 'Test_Qtypes/what is the woman': 37.73, 'Test_Qtypes/is the man': 66.25, 'Test_Qtypes/is this': 63.91, 'Test_Qtypes/is the person': 65.19, 'Test_Qtypes/is the woman': 70.1, 'Test_Qtypes/are': 85.71, 'Test_Qtypes/are these': 47.78, 'Test_Qtypes/what is the person': 46.9, 'Test_Qtypes/are the': 58.33, 'Test_Atypes/other': 39.34, 'Test_Atypes/yes_no': 69.06, 'Test_Atypes/number': 0.0}
q_causal {'Test/overall': 15.2, 'Test/topk_optimal': 18.45, 'Test/topk_not_optimal': 4.0, 'Test_Qtypes/why': 15.23, 'Test_Qtypes/why is the': 15.1, 'Test_Atypes/other': 15.28, 'Test_Atypes/number': 0.0}


#------------------ result_matrix --------------------#
q_location      q_recognition   q_count q_color q_type  q_subcategory   q_judge q_commonsense   q_action        q_causal

16.87   0.0     0.0     0.0     0.0     0.0     0.0     0.0     0.0     0.0     Avg: 1.69
26.35   24.57   0.0     0.0     0.0     0.0     0.0     0.0     0.0     0.0     Avg: 5.09
28.58   17.95   34.51   0.0     0.0     0.0     0.0     0.0     0.0     0.0     Avg: 8.1
26.09   19.81   31.81   56.78   0.0     0.0     0.0     0.0     0.0     0.0     Avg: 13.45
29.89   21.81   30.82   50.83   35.28   0.0     0.0     0.0     0.0     0.0     Avg: 16.86
29.38   22.14   32.11   51.74   31.67   45.71   0.0     0.0     0.0     0.0     Avg: 21.28
23.6    15.88   28.91   41.96   20.97   42.15   65.55   0.0     0.0     0.0     Avg: 23.9
22.88   17.49   31.7    44.54   23.31   42.08   64.3    63.41   0.0     0.0     Avg: 30.97
25.7    20.53   30.0    45.64   24.07   42.9    65.33   65.45   57.99   0.0     Avg: 37.76
22.83   19.82   30.66   44.24   28.25   42.82   64.13   66.5    57.2    15.2    Avg: 39.16
#------  Metric  ------#
Incremental avg accuracy: [np.float64(16.87), np.float64(25.46), np.float64(27.013333333333332), np.float64(33.6225), np.float64(33.726000000000006), np.float64(35.458333333333336), np.float64(34.145714285714284), np.float64(38.713750000000005), np.float64(41.95666666666667), np.float64(39.165)]
*** Avg accuracy *** 39.165
Incremental avg forget: [0, np.float64(-9.48), np.float64(2.195000000000002), np.float64(3.3166666666666664), np.float64(2.7725), np.float64(2.7979999999999996), np.float64(8.878333333333334), np.float64(6.570000000000001), np.float64(4.51), np.float64(4.3644444444444455)]
*** Avg forget *** 4.3644444444444455
6Q Incremental avg accuracy: [-1, np.float64(24.57), np.float64(17.95), np.float64(19.81), np.float64(28.545), np.float64(33.17333333333334), np.float64(36.1375), np.float64(42.118), np.float64(46.044999999999995), np.float64(46.453333333333326)]
*** _6Q Avg accuracy *** 46.453333333333326
_6Q Incremental avg forget: [0, np.float64(-9.48), np.float64(-2.229999999999997), np.float64(2.4899999999999984), np.float64(2.3200000000000003), np.float64(3.0533333333333332), np.float64(9.745000000000001), np.float64(7.2200000000000015), np.float64(4.588333333333333), np.float64(4.981666666666668)]
*** _6Q Avg forget *** 4.981666666666668


q_color {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.0, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.0, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.0}
q_count {'Test/overall': 36.68, 'Test/topk_optimal': 37.0, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 36.7, 'Test_Qtypes/how many people are': 41.95, 'Test_Qtypes/how many people are in': 46.63, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 36.98, 'Test_Atypes/other': 1.36}
q_count {'Test/overall': 36.68, 'Test/topk_optimal': 37.0, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 36.7, 'Test_Qtypes/how many people are': 41.95, 'Test_Qtypes/how many people are in': 46.63, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 36.98, 'Test_Atypes/other': 1.36}
q_color {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.0, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.0, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.0}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 30.86, 'Test/topk_optimal': 31.06, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 31.25, 'Test_Qtypes/what color': 29.14, 'Test_Qtypes/what color are the': 30.7, 'Test_Qtypes/what is the color of the': 39.76, 'Test_Qtypes/what color is': 23.59, 'Test_Atypes/other': 30.86}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 30.86, 'Test/topk_optimal': 31.06, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 31.25, 'Test_Qtypes/what color': 29.14, 'Test_Qtypes/what color are the': 30.7, 'Test_Qtypes/what is the color of the': 39.76, 'Test_Qtypes/what color is': 23.59, 'Test_Atypes/other': 30.86}
q_location {'Test/overall': 0.15, 'Test/topk_optimal': 0.17, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.38, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 30.86, 'Test/topk_optimal': 31.06, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 31.25, 'Test_Qtypes/what color': 29.14, 'Test_Qtypes/what color are the': 30.7, 'Test_Qtypes/what is the color of the': 39.76, 'Test_Qtypes/what color is': 23.59, 'Test_Atypes/other': 30.86}
q_location {'Test/overall': 0.15, 'Test/topk_optimal': 0.17, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.38, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 30.86, 'Test/topk_optimal': 31.06, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 31.25, 'Test_Qtypes/what color': 29.14, 'Test_Qtypes/what color are the': 30.7, 'Test_Qtypes/what is the color of the': 39.76, 'Test_Qtypes/what color is': 23.59, 'Test_Atypes/other': 30.86}
q_location {'Test/overall': 0.15, 'Test/topk_optimal': 0.17, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.38, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 30.86, 'Test/topk_optimal': 31.06, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 31.25, 'Test_Qtypes/what color': 29.14, 'Test_Qtypes/what color are the': 30.7, 'Test_Qtypes/what is the color of the': 39.76, 'Test_Qtypes/what color is': 23.59, 'Test_Atypes/other': 30.86}
q_location {'Test/overall': 0.15, 'Test/topk_optimal': 0.17, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.38, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_count {'Test/overall': 34.04, 'Test/topk_optimal': 34.34, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 33.66, 'Test_Qtypes/how many people are': 39.23, 'Test_Qtypes/how many people are in': 51.05, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 34.31, 'Test_Atypes/other': 2.73}
q_color {'Test/overall': 30.86, 'Test/topk_optimal': 31.06, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 31.25, 'Test_Qtypes/what color': 29.14, 'Test_Qtypes/what color are the': 30.7, 'Test_Qtypes/what is the color of the': 39.76, 'Test_Qtypes/what color is': 23.59, 'Test_Atypes/other': 30.86}
q_location {'Test/overall': 0.15, 'Test/topk_optimal': 0.17, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.38, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 33.4, 'Test/topk_optimal': 33.71, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 33.19, 'Test_Qtypes/how many people are': 40.69, 'Test_Qtypes/how many people are in': 40.42, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 33.67, 'Test_Atypes/other': 1.36}
q_color {'Test/overall': 30.4, 'Test/topk_optimal': 30.57, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 30.5, 'Test_Qtypes/what color': 24.57, 'Test_Qtypes/what color are the': 31.46, 'Test_Qtypes/what is the color of the': 42.82, 'Test_Qtypes/what color is': 25.56, 'Test_Atypes/other': 30.4}
q_location {'Test/overall': 0.2, 'Test/topk_optimal': 0.23, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.0, 'Test_Qtypes/where is the': 0.3, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.2, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 33.4, 'Test/topk_optimal': 33.71, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 33.19, 'Test_Qtypes/how many people are': 40.69, 'Test_Qtypes/how many people are in': 40.42, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 33.67, 'Test_Atypes/other': 1.36}
q_color {'Test/overall': 30.4, 'Test/topk_optimal': 30.57, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 30.5, 'Test_Qtypes/what color': 24.57, 'Test_Qtypes/what color are the': 31.46, 'Test_Qtypes/what is the color of the': 42.82, 'Test_Qtypes/what color is': 25.56, 'Test_Atypes/other': 30.4}
q_location {'Test/overall': 0.2, 'Test/topk_optimal': 0.23, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.0, 'Test_Qtypes/where is the': 0.3, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.2, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 33.4, 'Test/topk_optimal': 33.71, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 33.19, 'Test_Qtypes/how many people are': 40.69, 'Test_Qtypes/how many people are in': 40.42, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 33.67, 'Test_Atypes/other': 1.36}
q_count {'Test/overall': 33.4, 'Test/topk_optimal': 33.71, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 33.19, 'Test_Qtypes/how many people are': 40.69, 'Test_Qtypes/how many people are in': 40.42, 'Test_Qtypes/what number is': 1.45, 'Test_Atypes/number': 33.67, 'Test_Atypes/other': 1.36}
q_color {'Test/overall': 30.4, 'Test/topk_optimal': 30.57, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 30.5, 'Test_Qtypes/what color': 24.57, 'Test_Qtypes/what color are the': 31.46, 'Test_Qtypes/what is the color of the': 42.82, 'Test_Qtypes/what color is': 25.56, 'Test_Atypes/other': 30.4}
q_count {'Test/overall': 34.96, 'Test/topk_optimal': 35.27, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 34.55, 'Test_Qtypes/how many people are': 41.14, 'Test_Qtypes/how many people are in': 48.63, 'Test_Qtypes/what number is': 4.19, 'Test_Atypes/number': 35.22, 'Test_Atypes/other': 4.09}
q_color {'Test/overall': 38.29, 'Test/topk_optimal': 38.57, 'Test/topk_not_optimal': 13.75, 'Test_Qtypes/what color is the': 38.94, 'Test_Qtypes/what color': 28.61, 'Test_Qtypes/what color are the': 38.99, 'Test_Qtypes/what is the color of the': 49.29, 'Test_Qtypes/what color is': 33.59, 'Test_Atypes/other': 38.29}
q_location {'Test/overall': 0.1, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.37, 'Test_Qtypes/where are the': 0.19, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.1, 'Test_Atypes/number': 0.0}



----
q_recognition {'Test/overall': 26.01, 'Test/topk_optimal': 29.83, 'Test/topk_not_optimal': 2.48, 'Test_Qtypes/what': 25.92, 'Test_Qtypes/what is the': 25.56, 'Test_Qtypes/what is on the': 25.11, 'Test_Qtypes/what is': 22.43, 'Test_Qtypes/what is this': 36.07, 'Test_Qtypes/what are the': 26.86, 'Test_Qtypes/what are': 34.59, 'Test_Qtypes/what is in the': 32.63, 'Test_Qtypes/who is': 17.13, 'Test_Qtypes/what is the name': 6.99, 'Test_Qtypes/which': 34.35, 'Test_Qtypes/what does the': 18.45, 'Test_Atypes/other': 26.71, 'Test_Atypes/number': 6.89, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 8.47, 'Test/topk_optimal': 9.66, 'Test/topk_not_optimal': 1.14, 'Test_Qtypes/what': 9.92, 'Test_Qtypes/what is the': 9.22, 'Test_Qtypes/what is on the': 2.16, 'Test_Qtypes/what is': 4.53, 'Test_Qtypes/what is this': 16.43, 'Test_Qtypes/what are the': 7.2, 'Test_Qtypes/what are': 10.29, 'Test_Qtypes/what is in the': 4.19, 'Test_Qtypes/who is': 1.48, 'Test_Qtypes/what is the name': 1.78, 'Test_Qtypes/which': 19.85, 'Test_Qtypes/what does the': 1.64, 'Test_Atypes/other': 8.78, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 29.64, 'Test/topk_optimal': 33.21, 'Test/topk_not_optimal': 6.59, 'Test_Qtypes/where are the': 25.38, 'Test_Qtypes/where is the': 24.82, 'Test_Qtypes/what room is': 76.73, 'Test_Atypes/other': 29.69, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 0.65, 'Test/topk_optimal': 0.67, 'Test/topk_not_optimal': 0.57, 'Test_Qtypes/what': 0.75, 'Test_Qtypes/what is the': 0.6, 'Test_Qtypes/what is on the': 0.39, 'Test_Qtypes/what is': 0.75, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.52, 'Test_Qtypes/what are': 0.35, 'Test_Qtypes/what is in the': 0.28, 'Test_Qtypes/who is': 1.11, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.4, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.61, 'Test_Atypes/number': 0.92, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 1.08, 'Test/topk_optimal': 1.02, 'Test/topk_not_optimal': 1.46, 'Test_Qtypes/where are the': 1.14, 'Test_Qtypes/where is the': 1.21, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 1.08, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 65.82, 'Test/topk_optimal': 66.32, 'Test/topk_not_optimal': 7.38, 'Test_Qtypes/is there': 63.5, 'Test_Qtypes/is the': 66.67, 'Test_Qtypes/is this a': 67.73, 'Test_Qtypes/is there a': 65.89, 'Test_Qtypes/are the': 65.88, 'Test_Qtypes/is this': 65.2, 'Test_Qtypes/was': 68.57, 'Test_Qtypes/is': 64.72, 'Test_Qtypes/is it': 69.53, 'Test_Qtypes/are these': 64.09, 'Test_Qtypes/are there': 63.43, 'Test_Qtypes/is this an': 61.73, 'Test_Qtypes/are': 64.28, 'Test_Qtypes/is that a': 61.04, 'Test_Qtypes/are there any': 60.3, 'Test_Atypes/yes_no': 67.34, 'Test_Atypes/other': 46.36, 'Test_Atypes/number': 52.0}
q_recognition {'Test/overall': 0.73, 'Test/topk_optimal': 0.74, 'Test/topk_not_optimal': 0.65, 'Test_Qtypes/what': 0.71, 'Test_Qtypes/what is the': 0.8, 'Test_Qtypes/what is on the': 0.26, 'Test_Qtypes/what is': 0.92, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.76, 'Test_Qtypes/what are': 1.22, 'Test_Qtypes/what is in the': 0.14, 'Test_Qtypes/who is': 0.83, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.03, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.7, 'Test_Atypes/number': 0.61, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 3.34, 'Test/topk_optimal': 3.29, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 2.66, 'Test_Qtypes/where is the': 3.99, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 3.34, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 60.08, 'Test/topk_optimal': 60.54, 'Test/topk_not_optimal': 6.39, 'Test_Qtypes/is there': 59.95, 'Test_Qtypes/is the': 60.24, 'Test_Qtypes/is this a': 59.74, 'Test_Qtypes/is there a': 58.77, 'Test_Qtypes/are the': 58.96, 'Test_Qtypes/is this': 61.46, 'Test_Qtypes/was': 60.71, 'Test_Qtypes/is': 58.52, 'Test_Qtypes/is it': 58.82, 'Test_Qtypes/are these': 58.55, 'Test_Qtypes/are there': 66.31, 'Test_Qtypes/is this an': 61.02, 'Test_Qtypes/are': 61.62, 'Test_Qtypes/is that a': 54.48, 'Test_Qtypes/are there any': 58.37, 'Test_Atypes/yes_no': 61.82, 'Test_Atypes/other': 37.67, 'Test_Atypes/number': 58.0}
q_commonsense {'Test/overall': 67.74, 'Test/topk_optimal': 67.85, 'Test/topk_not_optimal': 42.0, 'Test_Qtypes/do': 65.97, 'Test_Qtypes/does the': 68.32, 'Test_Qtypes/does this': 70.04, 'Test_Qtypes/can you': 63.8, 'Test_Qtypes/has': 63.66, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.8, 'Test_Atypes/other': 25.19, 'Test_Atypes/number': 75.0}
q_recognition {'Test/overall': 0.28, 'Test/topk_optimal': 0.29, 'Test/topk_not_optimal': 0.23, 'Test_Qtypes/what': 0.65, 'Test_Qtypes/what is the': 0.15, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.0, 'Test_Qtypes/what is this': 0.0, 'Test_Qtypes/what are the': 0.15, 'Test_Qtypes/what are': 0.17, 'Test_Qtypes/what is in the': 0.0, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.55, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.08, 'Test_Atypes/number': 5.71, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 0.25, 'Test/topk_optimal': 0.28, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.76, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.25, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.13, 'Test/topk_optimal': 0.13, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/is there': 0.0, 'Test_Qtypes/is the': 0.02, 'Test_Qtypes/is this a': 0.0, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 0.35, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.17, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.28, 'Test_Qtypes/are there': 0.62, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.55, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 1.33, 'Test_Atypes/yes_no': 0.11, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 38.0}
q_commonsense {'Test/overall': 0.08, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.28, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 45.0}
q_count {'Test/overall': 37.15, 'Test/topk_optimal': 37.48, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 37.12, 'Test_Qtypes/how many people are': 41.22, 'Test_Qtypes/how many people are in': 49.47, 'Test_Qtypes/what number is': 3.23, 'Test_Atypes/number': 37.46, 'Test_Atypes/other': 0.0}
---q_recognition {'Test/overall': 26.01, 'Test/topk_optimal': 29.83, 'Test/topk_not_optimal': 2.48, 'Test_Qtypes/what': 25.92, 'Test_Qtypes/what is the': 25.56, 'Test_Qtypes/what is on the': 25.11, 'Test_Qtypes/what is': 22.43, 'Test_Qtypes/what is this': 36.07, 'Test_Qtypes/what are the': 26.86, 'Test_Qtypes/what are': 34.59, 'Test_Qtypes/what is in the': 32.63, 'Test_Qtypes/who is': 17.13, 'Test_Qtypes/what is the name': 6.99, 'Test_Qtypes/which': 34.35, 'Test_Qtypes/what does the': 18.45, 'Test_Atypes/other': 26.71, 'Test_Atypes/number': 6.89, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 17.23, 'Test/topk_optimal': 19.11, 'Test/topk_not_optimal': 5.12, 'Test_Qtypes/where are the': 14.18, 'Test_Qtypes/where is the': 12.01, 'Test_Qtypes/what room is': 63.82, 'Test_Atypes/other': 17.26, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 63.54, 'Test/topk_optimal': 64.05, 'Test/topk_not_optimal': 3.44, 'Test_Qtypes/is there': 59.67, 'Test_Qtypes/is the': 63.37, 'Test_Qtypes/is this a': 65.58, 'Test_Qtypes/is there a': 59.42, 'Test_Qtypes/are the': 62.98, 'Test_Qtypes/is this': 65.4, 'Test_Qtypes/was': 63.06, 'Test_Qtypes/is': 64.0, 'Test_Qtypes/is it': 68.2, 'Test_Qtypes/are these': 65.79, 'Test_Qtypes/are there': 57.88, 'Test_Qtypes/is this an': 61.84, 'Test_Qtypes/are': 64.02, 'Test_Qtypes/is that a': 62.71, 'Test_Qtypes/are there any': 62.59, 'Test_Atypes/yes_no': 65.42, 'Test_Atypes/other': 39.34, 'Test_Atypes/number': 44.0}
q_commonsense {'Test/overall': 67.17, 'Test/topk_optimal': 67.4, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 66.6, 'Test_Qtypes/does the': 67.03, 'Test_Qtypes/does this': 69.4, 'Test_Qtypes/can you': 63.04, 'Test_Qtypes/has': 63.09, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.85, 'Test_Atypes/other': 3.33, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 34.74, 'Test/topk_optimal': 35.05, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 34.68, 'Test_Qtypes/how many people are': 37.64, 'Test_Qtypes/how many people are in': 48.95, 'Test_Qtypes/what number is': 3.71, 'Test_Atypes/number': 35.03, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 44.62, 'Test/topk_optimal': 45.75, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 22.62, 'Test_Qtypes/are they': 60.18, 'Test_Qtypes/is this person': 59.62, 'Test_Qtypes/is he': 60.81, 'Test_Qtypes/what is the woman': 19.38, 'Test_Qtypes/is the man': 55.28, 'Test_Qtypes/is this': 38.7, 'Test_Qtypes/is the person': 53.42, 'Test_Qtypes/is the woman': 55.76, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 24.0, 'Test_Qtypes/are the': 51.11, 'Test_Atypes/other': 21.8, 'Test_Atypes/yes_no': 59.7, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 59.81, 'Test/topk_optimal': 60.3, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 60.13, 'Test_Qtypes/what color': 58.87, 'Test_Qtypes/what color are the': 56.13, 'Test_Qtypes/what is the color of the': 66.47, 'Test_Qtypes/what color is': 61.9, 'Test_Atypes/other': 59.81}
q_type {'Test/overall': 25.89, 'Test/topk_optimal': 28.07, 'Test/topk_not_optimal': 4.71, 'Test_Qtypes/what type of': 26.81, 'Test_Qtypes/what kind of': 25.24, 'Test_Atypes/other': 25.91, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 41.35, 'Test/topk_optimal': 45.11, 'Test/topk_not_optimal': 2.86, 'Test_Qtypes/none of the above': 39.05, 'Test_Qtypes/what time': 28.54, 'Test_Qtypes/what sport is': 66.54, 'Test_Qtypes/what brand': 27.85, 'Test_Qtypes/what animal is': 58.94, 'Test_Atypes/number': 4.94, 'Test_Atypes/other': 32.16, 'Test_Atypes/yes_no': 65.17}
q_causal {'Test/overall': 12.45, 'Test/topk_optimal': 13.55, 'Test/topk_not_optimal': 8.67, 'Test_Qtypes/why': 13.11, 'Test_Qtypes/why is the': 10.41, 'Test_Atypes/other': 12.36, 'Test_Atypes/number': 30.0}

q_recognition {'Test/overall': 0.69, 'Test/topk_optimal': 0.69, 'Test/topk_not_optimal': 0.65, 'Test_Qtypes/what': 0.69, 'Test_Qtypes/what is the': 0.72, 'Test_Qtypes/what is on the': 0.26, 'Test_Qtypes/what is': 0.92, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.76, 'Test_Qtypes/what are': 1.22, 'Test_Qtypes/what is in the': 0.14, 'Test_Qtypes/who is': 0.83, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.66, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.66, 'Test_Atypes/number': 0.46, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 3.29, 'Test/topk_optimal': 3.23, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 2.66, 'Test_Qtypes/where is the': 3.92, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 3.3, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 57.38, 'Test/topk_optimal': 57.84, 'Test/topk_not_optimal': 2.95, 'Test_Qtypes/is there': 58.93, 'Test_Qtypes/is the': 56.97, 'Test_Qtypes/is this a': 56.69, 'Test_Qtypes/is there a': 58.63, 'Test_Qtypes/are the': 55.22, 'Test_Qtypes/is this': 57.77, 'Test_Qtypes/was': 53.47, 'Test_Qtypes/is': 55.8, 'Test_Qtypes/is it': 53.77, 'Test_Qtypes/are these': 57.61, 'Test_Qtypes/are there': 65.85, 'Test_Qtypes/is this an': 61.02, 'Test_Qtypes/are': 61.03, 'Test_Qtypes/is that a': 51.25, 'Test_Qtypes/are there any': 58.15, 'Test_Atypes/yes_no': 61.53, 'Test_Atypes/other': 3.88, 'Test_Atypes/number': 36.0}
q_commonsense {'Test/overall': 66.9, 'Test/topk_optimal': 67.12, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 66.6, 'Test_Qtypes/does the': 66.12, 'Test_Qtypes/does this': 69.4, 'Test_Qtypes/can you': 63.04, 'Test_Qtypes/has': 63.09, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.57, 'Test_Atypes/other': 3.33, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 0.27, 'Test/topk_optimal': 0.27, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.31, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.27, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 36.16, 'Test/topk_optimal': 37.06, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 0.63, 'Test_Qtypes/are they': 60.78, 'Test_Qtypes/is this person': 59.1, 'Test_Qtypes/is he': 57.24, 'Test_Qtypes/what is the woman': 1.55, 'Test_Qtypes/is the man': 54.62, 'Test_Qtypes/is this': 38.26, 'Test_Qtypes/is the person': 48.23, 'Test_Qtypes/is the woman': 53.33, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 0.3, 'Test_Qtypes/are the': 51.11, 'Test_Atypes/other': 1.22, 'Test_Atypes/yes_no': 59.17, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.05, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.04, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.18, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.05}
q_type {'Test/overall': 0.06, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.29, 'Test_Qtypes/what type of': 0.0, 'Test_Qtypes/what kind of': 0.09, 'Test_Atypes/other': 0.06, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 24.1, 'Test/topk_optimal': 26.4, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 36.2, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.56, 'Test_Qtypes/what animal is': 1.59, 'Test_Atypes/number': 1.72, 'Test_Atypes/other': 1.26, 'Test_Atypes/yes_no': 70.56}
q_causal {'Test/overall': 12.45, 'Test/topk_optimal': 13.55, 'Test/topk_not_optimal': 8.67, 'Test_Qtypes/why': 13.11, 'Test_Qtypes/why is the': 10.41, 'Test_Atypes/other': 12.36, 'Test_Atypes/number': 30.0}
q_recognition {'Test/overall': 26.04, 'Test/topk_optimal': 29.87, 'Test/topk_not_optimal': 2.48, 'Test_Qtypes/what': 25.96, 'Test_Qtypes/what is the': 25.67, 'Test_Qtypes/what is on the': 25.11, 'Test_Qtypes/what is': 22.43, 'Test_Qtypes/what is this': 36.07, 'Test_Qtypes/what are the': 26.86, 'Test_Qtypes/what are': 34.59, 'Test_Qtypes/what is in the': 32.63, 'Test_Qtypes/who is': 17.13, 'Test_Qtypes/what is the name': 6.99, 'Test_Qtypes/which': 34.35, 'Test_Qtypes/what does the': 18.45, 'Test_Atypes/other': 26.74, 'Test_Atypes/number': 6.89, 'Test_Atypes/yes_no': 0.0}

q_recognition {'Test/overall': 26.04, 'Test/topk_optimal': 29.87, 'Test/topk_not_optimal': 2.48, 'Test_Qtypes/what': 25.96, 'Test_Qtypes/what is the': 25.67, 'Test_Qtypes/what is on the': 25.11, 'Test_Qtypes/what is': 22.43, 'Test_Qtypes/what is this': 36.07, 'Test_Qtypes/what are the': 26.86, 'Test_Qtypes/what are': 34.59, 'Test_Qtypes/what is in the': 32.63, 'Test_Qtypes/who is': 17.13, 'Test_Qtypes/what is the name': 6.99, 'Test_Qtypes/which': 34.35, 'Test_Qtypes/what does the': 18.45, 'Test_Atypes/other': 26.74, 'Test_Atypes/number': 6.89, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 16.96, 'Test/topk_optimal': 18.79, 'Test/topk_not_optimal': 5.12, 'Test_Qtypes/where are the': 14.18, 'Test_Qtypes/where is the': 12.01, 'Test_Qtypes/what room is': 60.73, 'Test_Atypes/other': 16.98, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 63.45, 'Test/topk_optimal': 63.97, 'Test/topk_not_optimal': 3.44, 'Test_Qtypes/is there': 59.67, 'Test_Qtypes/is the': 63.27, 'Test_Qtypes/is this a': 65.58, 'Test_Qtypes/is there a': 59.42, 'Test_Qtypes/are the': 62.82, 'Test_Qtypes/is this': 65.4, 'Test_Qtypes/was': 63.06, 'Test_Qtypes/is': 63.42, 'Test_Qtypes/is it': 67.96, 'Test_Qtypes/are these': 65.79, 'Test_Qtypes/are there': 57.88, 'Test_Qtypes/is this an': 61.84, 'Test_Qtypes/are': 64.02, 'Test_Qtypes/is that a': 62.71, 'Test_Qtypes/are there any': 62.59, 'Test_Atypes/yes_no': 65.33, 'Test_Atypes/other': 39.34, 'Test_Atypes/number': 44.0}
q_commonsense {'Test/overall': 67.17, 'Test/topk_optimal': 67.4, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 66.6, 'Test_Qtypes/does the': 67.03, 'Test_Qtypes/does this': 69.4, 'Test_Qtypes/can you': 63.04, 'Test_Qtypes/has': 63.09, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.85, 'Test_Atypes/other': 3.33, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 34.74, 'Test/topk_optimal': 35.05, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 34.68, 'Test_Qtypes/how many people are': 37.64, 'Test_Qtypes/how many people are in': 48.95, 'Test_Qtypes/what number is': 3.71, 'Test_Atypes/number': 35.03, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 44.6, 'Test/topk_optimal': 45.72, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 22.52, 'Test_Qtypes/are they': 60.18, 'Test_Qtypes/is this person': 59.62, 'Test_Qtypes/is he': 60.81, 'Test_Qtypes/what is the woman': 19.38, 'Test_Qtypes/is the man': 55.28, 'Test_Qtypes/is this': 38.7, 'Test_Qtypes/is the person': 53.42, 'Test_Qtypes/is the woman': 55.76, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 24.9, 'Test_Qtypes/are the': 45.56, 'Test_Atypes/other': 21.92, 'Test_Atypes/yes_no': 59.58, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 59.81, 'Test/topk_optimal': 60.3, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 60.13, 'Test_Qtypes/what color': 58.87, 'Test_Qtypes/what color are the': 56.13, 'Test_Qtypes/what is the color of the': 66.47, 'Test_Qtypes/what color is': 61.9, 'Test_Atypes/other': 59.81}
q_type {'Test/overall': 25.89, 'Test/topk_optimal': 28.07, 'Test/topk_not_optimal': 4.71, 'Test_Qtypes/what type of': 26.81, 'Test_Qtypes/what kind of': 25.24, 'Test_Atypes/other': 25.91, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 41.33, 'Test/topk_optimal': 45.09, 'Test/topk_not_optimal': 2.86, 'Test_Qtypes/none of the above': 39.02, 'Test_Qtypes/what time': 28.54, 'Test_Qtypes/what sport is': 66.54, 'Test_Qtypes/what brand': 27.85, 'Test_Qtypes/what animal is': 58.94, 'Test_Atypes/number': 4.94, 'Test_Atypes/other': 32.16, 'Test_Atypes/yes_no': 65.11}
q_causal {'Test/overall': 12.45, 'Test/topk_optimal': 13.55, 'Test/topk_not_optimal': 8.67, 'Test_Qtypes/why': 13.11, 'Test_Qtypes/why is the': 10.41, 'Test_Atypes/other': 12.36, 'Test_Atypes/number': 30.0}


---CHATGPT ORDER NO MEMORY---
q_subcategory {'Test/overall': 42.49, 'Test/topk_optimal': 46.2, 'Test/topk_not_optimal': 4.52, 'Test_Qtypes/none of the above': 41.36, 'Test_Qtypes/what time': 24.46, 'Test_Qtypes/what sport is': 72.46, 'Test_Qtypes/what brand': 32.71, 'Test_Qtypes/what animal is': 47.43, 'Test_Atypes/number': 5.4, 'Test_Atypes/other': 33.43, 'Test_Atypes/yes_no': 66.2}
q_subcategory {'Test/overall': 22.94, 'Test/topk_optimal': 25.13, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 34.64, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.28, 'Test_Qtypes/what animal is': 0.27, 'Test_Atypes/number': 0.34, 'Test_Atypes/other': 0.8, 'Test_Atypes/yes_no': 68.15}
q_commonsense {'Test/overall': 63.17, 'Test/topk_optimal': 63.41, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 64.34, 'Test_Qtypes/does the': 62.08, 'Test_Qtypes/does this': 62.38, 'Test_Qtypes/can you': 56.09, 'Test_Qtypes/has': 61.87, 'Test_Qtypes/could': 78.06, 'Test_Qtypes/do you': 65.36, 'Test_Atypes/yes_no': 64.04, 'Test_Atypes/other': 33.33, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 0.51, 'Test/topk_optimal': 0.56, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 0.77, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 0.83, 'Test_Atypes/yes_no': 0.0}
q_commonsense {'Test/overall': 0.05, 'Test/topk_optimal': 0.05, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.38, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.03, 'Test_Atypes/other': 1.11, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 55.85, 'Test/topk_optimal': 56.32, 'Test/topk_not_optimal': 13.75, 'Test_Qtypes/what color is the': 56.16, 'Test_Qtypes/what color': 53.64, 'Test_Qtypes/what color are the': 50.95, 'Test_Qtypes/what is the color of the': 65.06, 'Test_Qtypes/what color is': 60.85, 'Test_Atypes/other': 55.85}
q_subcategory {'Test/overall': 0.23, 'Test/topk_optimal': 0.25, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 0.34, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 3.68, 'Test_Atypes/other': 0.0, 'Test_Atypes/yes_no': 0.0}
q_commonsense {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.0, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.0, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.0}
q_count {'Test/overall': 36.06, 'Test/topk_optimal': 36.37, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 36.47, 'Test_Qtypes/how many people are': 35.2, 'Test_Qtypes/how many people are in': 49.58, 'Test_Qtypes/what number is': 3.71, 'Test_Atypes/number': 36.34, 'Test_Atypes/other': 2.73}
q_subcategory {'Test/overall': 11.88, 'Test/topk_optimal': 12.97, 'Test/topk_not_optimal': 0.71, 'Test_Qtypes/none of the above': 3.73, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 63.54, 'Test_Qtypes/what brand': 11.31, 'Test_Qtypes/what animal is': 34.16, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 19.42, 'Test_Atypes/yes_no': 0.13}
q_commonsense {'Test/overall': 0.32, 'Test/topk_optimal': 0.32, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.19, 'Test_Qtypes/does the': 0.89, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.24, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.06, 'Test_Atypes/other': 10.74, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 24.41, 'Test/topk_optimal': 24.56, 'Test/topk_not_optimal': 11.25, 'Test_Qtypes/what color is the': 25.23, 'Test_Qtypes/what color': 23.05, 'Test_Qtypes/what color are the': 23.96, 'Test_Qtypes/what is the color of the': 26.82, 'Test_Qtypes/what color is': 16.9, 'Test_Atypes/other': 24.41}
q_count {'Test/overall': 0.6, 'Test/topk_optimal': 0.61, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.61, 'Test_Qtypes/how many people are': 0.24, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 2.58, 'Test_Atypes/number': 0.61, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 19.57, 'Test/topk_optimal': 22.43, 'Test/topk_not_optimal': 1.98, 'Test_Qtypes/what': 21.19, 'Test_Qtypes/what is the': 20.09, 'Test_Qtypes/what is on the': 16.97, 'Test_Qtypes/what is': 14.67, 'Test_Qtypes/what is this': 25.61, 'Test_Qtypes/what are the': 15.01, 'Test_Qtypes/what are': 21.74, 'Test_Qtypes/what is in the': 21.8, 'Test_Qtypes/who is': 22.5, 'Test_Qtypes/what is the name': 3.01, 'Test_Qtypes/which': 28.41, 'Test_Qtypes/what does the': 14.61, 'Test_Atypes/other': 20.13, 'Test_Atypes/number': 4.08, 'Test_Atypes/yes_no': 0.0}
q_subcategory {'Test/overall': 13.79, 'Test/topk_optimal': 15.09, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 3.46, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 72.77, 'Test_Qtypes/what brand': 6.17, 'Test_Qtypes/what animal is': 54.6, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 22.56, 'Test_Atypes/yes_no': 0.13}
q_commonsense {'Test/overall': 0.25, 'Test/topk_optimal': 0.26, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.19, 'Test_Qtypes/does the': 0.4, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.33, 'Test_Qtypes/has': 0.49, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.36, 'Test_Atypes/yes_no': 0.14, 'Test_Atypes/other': 4.81, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 19.31, 'Test/topk_optimal': 19.48, 'Test/topk_not_optimal': 3.75, 'Test_Qtypes/what color is the': 19.05, 'Test_Qtypes/what color': 18.01, 'Test_Qtypes/what color are the': 21.68, 'Test_Qtypes/what is the color of the': 16.71, 'Test_Qtypes/what color is': 19.44, 'Test_Atypes/other': 19.31}
q_count {'Test/overall': 0.22, 'Test/topk_optimal': 0.22, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.09, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 6.13, 'Test_Atypes/number': 0.2, 'Test_Atypes/other': 2.73}
q_recognition {'Test/overall': 12.66, 'Test/topk_optimal': 14.48, 'Test/topk_not_optimal': 1.45, 'Test_Qtypes/what': 16.07, 'Test_Qtypes/what is the': 12.78, 'Test_Qtypes/what is on the': 10.48, 'Test_Qtypes/what is': 7.18, 'Test_Qtypes/what is this': 24.39, 'Test_Qtypes/what are the': 12.26, 'Test_Qtypes/what are': 11.98, 'Test_Qtypes/what is in the': 9.82, 'Test_Qtypes/who is': 2.31, 'Test_Qtypes/what is the name': 5.34, 'Test_Qtypes/which': 12.55, 'Test_Qtypes/what does the': 6.03, 'Test_Atypes/other': 13.05, 'Test_Atypes/number': 2.04, 'Test_Atypes/yes_no': 0.0}
q_type {'Test/overall': 26.8, 'Test/topk_optimal': 29.11, 'Test/topk_not_optimal': 4.41, 'Test_Qtypes/what type of': 26.74, 'Test_Qtypes/what kind of': 26.83, 'Test_Atypes/other': 26.82, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 5.83, 'Test/topk_optimal': 6.4, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 5.27, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 22.54, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 3.54, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 9.54, 'Test_Atypes/yes_no': 0.06}
q_commonsense {'Test/overall': 0.05, 'Test/topk_optimal': 0.05, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.24, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.06, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.21, 'Test/topk_optimal': 0.21, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.15, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.18, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 1.13, 'Test_Atypes/other': 0.21}
q_count {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.0, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 4.7, 'Test/topk_optimal': 5.32, 'Test/topk_not_optimal': 0.88, 'Test_Qtypes/what': 4.8, 'Test_Qtypes/what is the': 4.19, 'Test_Qtypes/what is on the': 0.82, 'Test_Qtypes/what is': 1.68, 'Test_Qtypes/what is this': 10.46, 'Test_Qtypes/what are the': 4.32, 'Test_Qtypes/what are': 4.77, 'Test_Qtypes/what is in the': 3.04, 'Test_Qtypes/who is': 1.48, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 21.07, 'Test_Qtypes/what does the': 0.73, 'Test_Atypes/other': 4.82, 'Test_Atypes/number': 1.33, 'Test_Atypes/yes_no': 0.0}
q_type {'Test/overall': 3.99, 'Test/topk_optimal': 4.35, 'Test/topk_not_optimal': 0.59, 'Test_Qtypes/what type of': 4.17, 'Test_Qtypes/what kind of': 3.87, 'Test_Atypes/other': 4.0, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 22.39, 'Test/topk_optimal': 25.52, 'Test/topk_not_optimal': 2.2, 'Test_Qtypes/where are the': 21.01, 'Test_Qtypes/where is the': 16.73, 'Test_Qtypes/what room is': 67.27, 'Test_Atypes/other': 22.43, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 27.75, 'Test/topk_optimal': 30.44, 'Test/topk_not_optimal': 0.24, 'Test_Qtypes/none of the above': 33.03, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 28.38, 'Test_Qtypes/what brand': 0.28, 'Test_Qtypes/what animal is': 41.24, 'Test_Atypes/number': 1.03, 'Test_Atypes/other': 12.16, 'Test_Atypes/yes_no': 61.63}
q_commonsense {'Test/overall': 56.37, 'Test/topk_optimal': 56.58, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 65.85, 'Test_Qtypes/does the': 53.06, 'Test_Qtypes/does this': 54.96, 'Test_Qtypes/can you': 57.39, 'Test_Qtypes/has': 43.09, 'Test_Qtypes/could': 72.54, 'Test_Qtypes/do you': 60.95, 'Test_Atypes/yes_no': 57.73, 'Test_Atypes/other': 6.67, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.65, 'Test/topk_optimal': 0.65, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.62, 'Test_Qtypes/what color': 1.32, 'Test_Qtypes/what color are the': 0.91, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.65}
q_count {'Test/overall': 0.18, 'Test/topk_optimal': 0.18, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.21, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.18, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 13.28, 'Test/topk_optimal': 15.2, 'Test/topk_not_optimal': 1.49, 'Test_Qtypes/what': 10.92, 'Test_Qtypes/what is the': 14.94, 'Test_Qtypes/what is on the': 14.2, 'Test_Qtypes/what is': 14.23, 'Test_Qtypes/what is this': 25.97, 'Test_Qtypes/what are the': 15.04, 'Test_Qtypes/what are': 22.5, 'Test_Qtypes/what is in the': 21.2, 'Test_Qtypes/who is': 3.52, 'Test_Qtypes/what is the name': 1.78, 'Test_Qtypes/which': 7.38, 'Test_Qtypes/what does the': 5.02, 'Test_Atypes/other': 13.75, 'Test_Atypes/number': 0.46, 'Test_Atypes/yes_no': 0.0}
q_type {'Test/overall': 14.31, 'Test/topk_optimal': 15.63, 'Test/topk_not_optimal': 1.47, 'Test_Qtypes/what type of': 14.68, 'Test_Qtypes/what kind of': 14.04, 'Test_Atypes/other': 14.32, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 7.91, 'Test/topk_optimal': 8.96, 'Test/topk_not_optimal': 1.1, 'Test_Qtypes/where are the': 5.06, 'Test_Qtypes/where is the': 4.77, 'Test_Qtypes/what room is': 38.73, 'Test_Atypes/other': 7.92, 'Test_Atypes/number': 0.0}
q_action {'Test/overall': 51.73, 'Test/topk_optimal': 53.06, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 35.17, 'Test_Qtypes/are they': 58.01, 'Test_Qtypes/is this person': 58.97, 'Test_Qtypes/is he': 59.19, 'Test_Qtypes/what is the woman': 29.48, 'Test_Qtypes/is the man': 60.24, 'Test_Qtypes/is this': 70.43, 'Test_Qtypes/is the person': 57.22, 'Test_Qtypes/is the woman': 70.71, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 27.78, 'Test_Qtypes/what is the person': 45.6, 'Test_Qtypes/are the': 55.0, 'Test_Atypes/other': 34.0, 'Test_Atypes/yes_no': 63.48, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 24.91, 'Test/topk_optimal': 27.25, 'Test/topk_not_optimal': 0.95, 'Test_Qtypes/none of the above': 37.49, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.56, 'Test_Qtypes/what animal is': 1.06, 'Test_Atypes/number': 0.69, 'Test_Atypes/other': 2.46, 'Test_Atypes/yes_no': 71.01}
q_commonsense {'Test/overall': 65.95, 'Test/topk_optimal': 66.19, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 65.35, 'Test_Qtypes/does the': 67.37, 'Test_Qtypes/does this': 67.1, 'Test_Qtypes/can you': 63.04, 'Test_Qtypes/has': 60.89, 'Test_Qtypes/could': 73.28, 'Test_Qtypes/do you': 62.86, 'Test_Atypes/yes_no': 67.53, 'Test_Atypes/other': 5.93, 'Test_Atypes/number': 30.0}
q_color {'Test/overall': 0.05, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.04, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.18, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.05}
q_count {'Test/overall': 0.24, 'Test/topk_optimal': 0.24, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.27, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.24, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 0.72, 'Test/topk_optimal': 0.76, 'Test/topk_not_optimal': 0.5, 'Test_Qtypes/what': 0.6, 'Test_Qtypes/what is the': 0.72, 'Test_Qtypes/what is on the': 0.26, 'Test_Qtypes/what is': 0.96, 'Test_Qtypes/what is this': 0.92, 'Test_Qtypes/what are the': 0.76, 'Test_Qtypes/what are': 1.22, 'Test_Qtypes/what is in the': 0.28, 'Test_Qtypes/who is': 1.11, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.51, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.7, 'Test_Atypes/number': 0.31, 'Test_Atypes/yes_no': 100.0}
q_type {'Test/overall': 0.06, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.29, 'Test_Qtypes/what type of': 0.0, 'Test_Qtypes/what kind of': 0.09, 'Test_Atypes/other': 0.06, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 3.19, 'Test/topk_optimal': 3.12, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 2.47, 'Test_Qtypes/where is the': 3.84, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 3.2, 'Test_Atypes/number': 0.0}
q_action {'Test/overall': 37.84, 'Test/topk_optimal': 38.83, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 0.52, 'Test_Qtypes/are they': 63.73, 'Test_Qtypes/is this person': 61.28, 'Test_Qtypes/is he': 57.48, 'Test_Qtypes/what is the woman': 1.55, 'Test_Qtypes/is the man': 57.29, 'Test_Qtypes/is this': 36.96, 'Test_Qtypes/is the person': 58.86, 'Test_Qtypes/is the woman': 54.04, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 0.3, 'Test_Qtypes/are the': 51.11, 'Test_Atypes/other': 3.57, 'Test_Atypes/yes_no': 60.41, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 59.9, 'Test/topk_optimal': 60.39, 'Test/topk_not_optimal': 2.95, 'Test_Qtypes/is there': 59.48, 'Test_Qtypes/is the': 60.59, 'Test_Qtypes/is this a': 59.27, 'Test_Qtypes/is there a': 57.96, 'Test_Qtypes/are the': 59.6, 'Test_Qtypes/is this': 59.28, 'Test_Qtypes/was': 55.41, 'Test_Qtypes/is': 57.45, 'Test_Qtypes/is it': 63.62, 'Test_Qtypes/are these': 60.13, 'Test_Qtypes/are there': 65.69, 'Test_Qtypes/is this an': 58.06, 'Test_Qtypes/are': 60.0, 'Test_Qtypes/is that a': 53.44, 'Test_Qtypes/are there any': 58.15, 'Test_Atypes/yes_no': 61.95, 'Test_Atypes/other': 33.68, 'Test_Atypes/number': 32.0}
q_subcategory {'Test/overall': 13.74, 'Test/topk_optimal': 15.04, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 18.92, 'Test_Qtypes/what time': 10.15, 'Test_Qtypes/what sport is': 3.08, 'Test_Qtypes/what brand': 0.28, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 0.69, 'Test_Atypes/other': 2.97, 'Test_Atypes/yes_no': 36.14}
q_commonsense {'Test/overall': 39.05, 'Test/topk_optimal': 39.18, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 41.7, 'Test_Qtypes/does the': 35.87, 'Test_Qtypes/does this': 41.29, 'Test_Qtypes/can you': 43.8, 'Test_Qtypes/has': 33.09, 'Test_Qtypes/could': 41.64, 'Test_Qtypes/do you': 41.31, 'Test_Atypes/yes_no': 40.0, 'Test_Atypes/other': 2.22, 'Test_Atypes/number': 30.0}
q_color {'Test/overall': 0.04, 'Test/topk_optimal': 0.04, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.02, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.18, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.04}
q_count {'Test/overall': 0.15, 'Test/topk_optimal': 0.15, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.17, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.15, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 0.98, 'Test/topk_optimal': 1.05, 'Test/topk_not_optimal': 0.57, 'Test_Qtypes/what': 0.97, 'Test_Qtypes/what is the': 0.98, 'Test_Qtypes/what is on the': 0.82, 'Test_Qtypes/what is': 0.42, 'Test_Qtypes/what is this': 1.63, 'Test_Qtypes/what are the': 2.29, 'Test_Qtypes/what are': 2.21, 'Test_Qtypes/what is in the': 0.14, 'Test_Qtypes/who is': 1.2, 'Test_Qtypes/what is the name': 1.37, 'Test_Qtypes/which': 0.55, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.97, 'Test_Atypes/number': 0.31, 'Test_Atypes/yes_no': 100.0}
q_type {'Test/overall': 0.2, 'Test/topk_optimal': 0.16, 'Test/topk_not_optimal': 0.59, 'Test_Qtypes/what type of': 0.13, 'Test_Qtypes/what kind of': 0.25, 'Test_Atypes/other': 0.2, 'Test_Atypes/number': 0.0}
q_location {'Test/overall': 1.72, 'Test/topk_optimal': 1.76, 'Test/topk_not_optimal': 1.46, 'Test_Qtypes/where are the': 2.09, 'Test_Qtypes/where is the': 1.73, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 1.72, 'Test_Atypes/number': 0.0}
q_action {'Test/overall': 15.86, 'Test/topk_optimal': 16.23, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 1.43, 'Test_Qtypes/are they': 26.87, 'Test_Qtypes/is this person': 23.85, 'Test_Qtypes/is he': 20.81, 'Test_Qtypes/what is the woman': 1.96, 'Test_Qtypes/is the man': 19.51, 'Test_Qtypes/is this': 23.91, 'Test_Qtypes/is the person': 24.68, 'Test_Qtypes/is the woman': 34.04, 'Test_Qtypes/are': 14.29, 'Test_Qtypes/are these': 11.11, 'Test_Qtypes/what is the person': 3.0, 'Test_Qtypes/are the': 16.67, 'Test_Atypes/other': 2.41, 'Test_Atypes/yes_no': 24.71, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 32.63, 'Test/topk_optimal': 32.89, 'Test/topk_not_optimal': 2.46, 'Test_Qtypes/is there': 37.05, 'Test_Qtypes/is the': 32.36, 'Test_Qtypes/is this a': 33.09, 'Test_Qtypes/is there a': 44.4, 'Test_Qtypes/are the': 32.8, 'Test_Qtypes/is this': 30.01, 'Test_Qtypes/was': 29.69, 'Test_Qtypes/is': 30.41, 'Test_Qtypes/is it': 8.23, 'Test_Qtypes/are these': 28.65, 'Test_Qtypes/are there': 44.9, 'Test_Qtypes/is this an': 34.69, 'Test_Qtypes/are': 36.16, 'Test_Qtypes/is that a': 40.0, 'Test_Qtypes/are there any': 43.63, 'Test_Atypes/yes_no': 34.59, 'Test_Atypes/other': 7.42, 'Test_Atypes/number': 12.0}
q_causal {'Test/overall': 8.65, 'Test/topk_optimal': 10.0, 'Test/topk_not_optimal': 4.0, 'Test_Qtypes/why': 9.01, 'Test_Qtypes/why is the': 7.55, 'Test_Atypes/other': 8.54, 'Test_Atypes/number': 30.0}



____INDEPENDT ON ALL TASKS____

q_recognition {'Test/overall': 26.01, 'Test/topk_optimal': 29.83, 'Test/topk_not_optimal': 2.48, 'Test_Qtypes/what': 25.92, 'Test_Qtypes/what is the': 25.56, 'Test_Qtypes/what is on the': 25.11, 'Test_Qtypes/what is': 22.43, 'Test_Qtypes/what is this': 36.07, 'Test_Qtypes/what are the': 26.86, 'Test_Qtypes/what are': 34.59, 'Test_Qtypes/what is in the': 32.63, 'Test_Qtypes/who is': 17.13, 'Test_Qtypes/what is the name': 6.99, 'Test_Qtypes/which': 34.35, 'Test_Qtypes/what does the': 18.45, 'Test_Atypes/other': 26.71, 'Test_Atypes/number': 6.89, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 8.71, 'Test/topk_optimal': 9.94, 'Test/topk_not_optimal': 0.73, 'Test_Qtypes/where are the': 4.68, 'Test_Qtypes/where is the': 5.43, 'Test_Qtypes/what room is': 44.0, 'Test_Atypes/other': 8.72, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.34, 'Test/topk_optimal': 0.33, 'Test/topk_not_optimal': 1.48, 'Test_Qtypes/is there': 0.44, 'Test_Qtypes/is the': 0.38, 'Test_Qtypes/is this a': 0.26, 'Test_Qtypes/is there a': 0.12, 'Test_Qtypes/are the': 0.53, 'Test_Qtypes/is this': 0.5, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.72, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.57, 'Test_Qtypes/are there': 0.1, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.22, 'Test_Atypes/yes_no': 0.16, 'Test_Atypes/other': 2.62, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.5, 'Test/topk_optimal': 0.5, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.19, 'Test_Qtypes/does the': 0.7, 'Test_Qtypes/does this': 0.4, 'Test_Qtypes/can you': 1.41, 'Test_Qtypes/has': 0.49, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.14, 'Test_Atypes/other': 14.81, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 9.29, 'Test/topk_optimal': 9.37, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 9.86, 'Test_Qtypes/how many people are': 7.6, 'Test_Qtypes/how many people are in': 4.84, 'Test_Qtypes/what number is': 2.1, 'Test_Atypes/number': 9.37, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 13.45, 'Test/topk_optimal': 13.64, 'Test/topk_not_optimal': 6.0, 'Test_Qtypes/what is the man': 36.57, 'Test_Qtypes/are they': 0.36, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 36.7, 'Test_Qtypes/is the man': 0.21, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 43.2, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 33.89, 'Test_Atypes/yes_no': 0.07, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 21.99, 'Test/topk_optimal': 22.01, 'Test/topk_not_optimal': 20.0, 'Test_Qtypes/what color is the': 21.66, 'Test_Qtypes/what color': 17.75, 'Test_Qtypes/what color are the': 20.67, 'Test_Qtypes/what is the color of the': 35.88, 'Test_Qtypes/what color is': 24.72, 'Test_Atypes/other': 21.99}
q_type {'Test/overall': 20.64, 'Test/topk_optimal': 22.62, 'Test/topk_not_optimal': 1.47, 'Test_Qtypes/what type of': 22.31, 'Test_Qtypes/what kind of': 19.47, 'Test_Atypes/other': 20.66, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 15.25, 'Test/topk_optimal': 16.6, 'Test/topk_not_optimal': 1.43, 'Test_Qtypes/none of the above': 5.26, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 70.38, 'Test_Qtypes/what brand': 20.75, 'Test_Qtypes/what animal is': 46.9, 'Test_Atypes/number': 0.34, 'Test_Atypes/other': 24.83, 'Test_Atypes/yes_no': 0.28}
q_causal {'Test/overall': 3.2, 'Test/topk_optimal': 3.94, 'Test/topk_not_optimal': 0.67, 'Test_Qtypes/why': 3.44, 'Test_Qtypes/why is the': 2.45, 'Test_Atypes/other': 3.22, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 26.01, 'q_location': 8.71, 'q_judge': 0.34, 'q_commonsense': 0.5, 'q_count': 9.29, 'q_action': 13.45, 'q_color': 21.99, 'q_type': 20.64, 'q_subcategory': 15.25, 'q_causal': 3.2}
AP: 11.938
q_recognition {'Test/overall': 0.63, 'Test/topk_optimal': 0.73, 'Test/topk_not_optimal': 0.04, 'Test_Qtypes/what': 0.64, 'Test_Qtypes/what is the': 0.26, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.36, 'Test_Qtypes/what is this': 2.81, 'Test_Qtypes/what are the': 0.79, 'Test_Qtypes/what are': 0.17, 'Test_Qtypes/what is in the': 0.83, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 2.69, 'Test_Qtypes/what does the': 0.14, 'Test_Atypes/other': 0.65, 'Test_Atypes/number': 0.15, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 17.23, 'Test/topk_optimal': 19.11, 'Test/topk_not_optimal': 5.12, 'Test_Qtypes/where are the': 14.18, 'Test_Qtypes/where is the': 12.01, 'Test_Qtypes/what room is': 63.82, 'Test_Atypes/other': 17.26, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.03, 'Test/topk_optimal': 0.02, 'Test/topk_not_optimal': 0.49, 'Test_Qtypes/is there': 0.0, 'Test_Qtypes/is the': 0.02, 'Test_Qtypes/is this a': 0.0, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 0.0, 'Test_Qtypes/is this': 0.03, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.29, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/are there': 0.1, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.31, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.0, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 0.12, 'Test/topk_optimal': 0.12, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 0.0, 'Test_Qtypes/are they': 0.0, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.81, 'Test_Qtypes/what is the woman': 0.62, 'Test_Qtypes/is the man': 0.0, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 0.0, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 0.29, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.0, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.0, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.0}
q_type {'Test/overall': 2.23, 'Test/topk_optimal': 2.43, 'Test/topk_not_optimal': 0.29, 'Test_Qtypes/what type of': 2.55, 'Test_Qtypes/what kind of': 2.01, 'Test_Atypes/other': 2.23, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 1.19, 'Test/topk_optimal': 1.31, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 1.81, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 1.96, 'Test_Atypes/yes_no': 0.0}
q_causal {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 0.0, 'Test_Qtypes/why is the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 0.63, 'q_location': 17.23, 'q_judge': 0.03, 'q_commonsense': 0.0, 'q_count': 0.0, 'q_action': 0.12, 'q_color': 0.0, 'q_type': 2.23, 'q_subcategory': 1.19, 'q_causal': 0.0}
AP: 2.143
q_recognition {'Test/overall': 0.42, 'Test/topk_optimal': 0.44, 'Test/topk_not_optimal': 0.3, 'Test_Qtypes/what': 0.37, 'Test_Qtypes/what is the': 0.41, 'Test_Qtypes/what is on the': 0.13, 'Test_Qtypes/what is': 0.33, 'Test_Qtypes/what is this': 0.31, 'Test_Qtypes/what are the': 0.59, 'Test_Qtypes/what are': 0.17, 'Test_Qtypes/what is in the': 0.55, 'Test_Qtypes/who is': 0.83, 'Test_Qtypes/what is the name': 0.41, 'Test_Qtypes/which': 1.29, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.41, 'Test_Atypes/number': 0.61, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 0.44, 'Test/topk_optimal': 0.34, 'Test/topk_not_optimal': 1.1, 'Test_Qtypes/where are the': 0.38, 'Test_Qtypes/where is the': 0.53, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.44, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 63.54, 'Test/topk_optimal': 64.05, 'Test/topk_not_optimal': 3.44, 'Test_Qtypes/is there': 59.67, 'Test_Qtypes/is the': 63.37, 'Test_Qtypes/is this a': 65.58, 'Test_Qtypes/is there a': 59.42, 'Test_Qtypes/are the': 62.98, 'Test_Qtypes/is this': 65.4, 'Test_Qtypes/was': 63.06, 'Test_Qtypes/is': 64.0, 'Test_Qtypes/is it': 68.2, 'Test_Qtypes/are these': 65.79, 'Test_Qtypes/are there': 57.88, 'Test_Qtypes/is this an': 61.84, 'Test_Qtypes/are': 64.02, 'Test_Qtypes/is that a': 62.71, 'Test_Qtypes/are there any': 62.59, 'Test_Atypes/yes_no': 65.42, 'Test_Atypes/other': 39.34, 'Test_Atypes/number': 44.0}
q_commonsense {'Test/overall': 60.76, 'Test/topk_optimal': 60.93, 'Test/topk_not_optimal': 24.0, 'Test_Qtypes/do': 58.81, 'Test_Qtypes/does the': 60.37, 'Test_Qtypes/does this': 61.94, 'Test_Qtypes/can you': 54.35, 'Test_Qtypes/has': 61.3, 'Test_Qtypes/could': 67.31, 'Test_Qtypes/do you': 63.57, 'Test_Atypes/yes_no': 61.45, 'Test_Atypes/other': 38.15, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.12, 'Test/topk_optimal': 0.13, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.13, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.13, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 40.95, 'Test/topk_optimal': 42.02, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 0.63, 'Test_Qtypes/are they': 56.81, 'Test_Qtypes/is this person': 62.69, 'Test_Qtypes/is he': 62.85, 'Test_Qtypes/what is the woman': 0.62, 'Test_Qtypes/is the man': 63.82, 'Test_Qtypes/is this': 74.78, 'Test_Qtypes/is the person': 61.27, 'Test_Qtypes/is the woman': 70.51, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 57.78, 'Test_Qtypes/what is the person': 0.0, 'Test_Qtypes/are the': 66.11, 'Test_Atypes/other': 4.31, 'Test_Atypes/yes_no': 64.95, 'Test_Atypes/number': 50.0}
q_color {'Test/overall': 0.05, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.02, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.27, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.05}
q_type {'Test/overall': 0.19, 'Test/topk_optimal': 0.21, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what type of': 0.33, 'Test_Qtypes/what kind of': 0.09, 'Test_Atypes/other': 0.19, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 22.3, 'Test/topk_optimal': 24.43, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 33.66, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.28, 'Test_Qtypes/what animal is': 0.27, 'Test_Atypes/number': 0.34, 'Test_Atypes/other': 1.41, 'Test_Atypes/yes_no': 65.06}
q_causal {'Test/overall': 5.95, 'Test/topk_optimal': 6.52, 'Test/topk_not_optimal': 4.0, 'Test_Qtypes/why': 6.03, 'Test_Qtypes/why is the': 5.71, 'Test_Atypes/other': 5.98, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 0.42, 'q_location': 0.44, 'q_judge': 63.54, 'q_commonsense': 60.76, 'q_count': 0.12, 'q_action': 40.95, 'q_color': 0.05, 'q_type': 0.19, 'q_subcategory': 22.3, 'q_causal': 5.95}
AP: 19.472
q_recognition {'Test/overall': 0.69, 'Test/topk_optimal': 0.69, 'Test/topk_not_optimal': 0.65, 'Test_Qtypes/what': 0.69, 'Test_Qtypes/what is the': 0.72, 'Test_Qtypes/what is on the': 0.26, 'Test_Qtypes/what is': 0.92, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.76, 'Test_Qtypes/what are': 1.22, 'Test_Qtypes/what is in the': 0.14, 'Test_Qtypes/who is': 0.83, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.66, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.66, 'Test_Atypes/number': 0.46, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 3.34, 'Test/topk_optimal': 3.29, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 2.66, 'Test_Qtypes/where is the': 3.99, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 3.34, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 57.6, 'Test/topk_optimal': 58.07, 'Test/topk_not_optimal': 2.95, 'Test_Qtypes/is there': 59.75, 'Test_Qtypes/is the': 57.26, 'Test_Qtypes/is this a': 56.69, 'Test_Qtypes/is there a': 58.63, 'Test_Qtypes/are the': 55.55, 'Test_Qtypes/is this': 57.88, 'Test_Qtypes/was': 53.47, 'Test_Qtypes/is': 56.38, 'Test_Qtypes/is it': 53.77, 'Test_Qtypes/are these': 57.92, 'Test_Qtypes/are there': 66.18, 'Test_Qtypes/is this an': 61.02, 'Test_Qtypes/are': 61.37, 'Test_Qtypes/is that a': 51.25, 'Test_Qtypes/are there any': 58.15, 'Test_Atypes/yes_no': 61.78, 'Test_Atypes/other': 3.88, 'Test_Atypes/number': 36.0}
q_commonsense {'Test/overall': 67.17, 'Test/topk_optimal': 67.4, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 66.6, 'Test_Qtypes/does the': 67.03, 'Test_Qtypes/does this': 69.4, 'Test_Qtypes/can you': 63.04, 'Test_Qtypes/has': 63.09, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.85, 'Test_Atypes/other': 3.33, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 0.27, 'Test/topk_optimal': 0.27, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.31, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.27, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 36.38, 'Test/topk_optimal': 37.29, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 0.63, 'Test_Qtypes/are they': 60.78, 'Test_Qtypes/is this person': 59.1, 'Test_Qtypes/is he': 57.24, 'Test_Qtypes/what is the woman': 1.55, 'Test_Qtypes/is the man': 54.97, 'Test_Qtypes/is this': 38.26, 'Test_Qtypes/is the person': 49.49, 'Test_Qtypes/is the woman': 54.34, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 0.3, 'Test_Qtypes/are the': 51.11, 'Test_Atypes/other': 1.22, 'Test_Atypes/yes_no': 59.53, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.05, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.04, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.18, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.05}
q_type {'Test/overall': 0.06, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.29, 'Test_Qtypes/what type of': 0.0, 'Test_Qtypes/what kind of': 0.09, 'Test_Atypes/other': 0.06, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 24.17, 'Test/topk_optimal': 26.48, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 36.3, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.56, 'Test_Qtypes/what animal is': 1.59, 'Test_Atypes/number': 1.72, 'Test_Atypes/other': 1.26, 'Test_Atypes/yes_no': 70.77}
q_causal {'Test/overall': 12.15, 'Test/topk_optimal': 13.16, 'Test/topk_not_optimal': 8.67, 'Test_Qtypes/why': 12.72, 'Test_Qtypes/why is the': 10.41, 'Test_Atypes/other': 12.06, 'Test_Atypes/number': 30.0}
#------------------ Final Performance --------------------#
{'q_recognition': 0.69, 'q_location': 3.34, 'q_judge': 57.6, 'q_commonsense': 67.17, 'q_count': 0.27, 'q_action': 36.38, 'q_color': 0.05, 'q_type': 0.06, 'q_subcategory': 24.17, 'q_causal': 12.15}
AP: 20.188
q_recognition {'Test/overall': 0.27, 'Test/topk_optimal': 0.29, 'Test/topk_not_optimal': 0.19, 'Test_Qtypes/what': 0.71, 'Test_Qtypes/what is the': 0.05, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.04, 'Test_Qtypes/what is this': 0.0, 'Test_Qtypes/what are the': 0.25, 'Test_Qtypes/what are': 0.0, 'Test_Qtypes/what is in the': 0.0, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.33, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.07, 'Test_Atypes/number': 5.82, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 0.1, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.37, 'Test_Qtypes/where are the': 0.19, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.1, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.16, 'Test/topk_optimal': 0.16, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/is there': 0.0, 'Test_Qtypes/is the': 0.0, 'Test_Qtypes/is this a': 0.0, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 0.21, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.17, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.69, 'Test_Qtypes/are there': 1.01, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.77, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 1.56, 'Test_Atypes/yes_no': 0.12, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 72.0}
q_commonsense {'Test/overall': 0.03, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.19, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.03, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 34.74, 'Test/topk_optimal': 35.05, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 34.68, 'Test_Qtypes/how many people are': 37.64, 'Test_Qtypes/how many people are in': 48.95, 'Test_Qtypes/what number is': 3.71, 'Test_Atypes/number': 35.03, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 0.07, 'Test/topk_optimal': 0.07, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 0.0, 'Test_Qtypes/are they': 0.0, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 0.0, 'Test_Qtypes/is the man': 0.35, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 0.0, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/number': 50.0}
q_color {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.0, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.0, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.0}
q_type {'Test/overall': 0.23, 'Test/topk_optimal': 0.25, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what type of': 0.07, 'Test_Qtypes/what kind of': 0.34, 'Test_Atypes/other': 0.23, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 0.32, 'Test/topk_optimal': 0.29, 'Test/topk_not_optimal': 0.71, 'Test_Qtypes/none of the above': 0.33, 'Test_Qtypes/what time': 1.15, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 4.25, 'Test_Atypes/other': 0.0, 'Test_Atypes/yes_no': 0.19}
q_causal {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 0.0, 'Test_Qtypes/why is the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 0.27, 'q_location': 0.1, 'q_judge': 0.16, 'q_commonsense': 0.03, 'q_count': 34.74, 'q_action': 0.07, 'q_color': 0.0, 'q_type': 0.23, 'q_subcategory': 0.32, 'q_causal': 0.0}
AP: 3.592
q_recognition {'Test/overall': 6.06, 'Test/topk_optimal': 6.92, 'Test/topk_not_optimal': 0.72, 'Test_Qtypes/what': 3.72, 'Test_Qtypes/what is the': 7.87, 'Test_Qtypes/what is on the': 7.14, 'Test_Qtypes/what is': 8.53, 'Test_Qtypes/what is this': 10.77, 'Test_Qtypes/what are the': 5.82, 'Test_Qtypes/what are': 9.36, 'Test_Qtypes/what is in the': 10.41, 'Test_Qtypes/who is': 1.39, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.25, 'Test_Qtypes/what does the': 4.98, 'Test_Atypes/other': 6.26, 'Test_Atypes/number': 0.61, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 2.21, 'Test/topk_optimal': 2.04, 'Test/topk_not_optimal': 3.29, 'Test_Qtypes/where are the': 2.66, 'Test_Qtypes/where is the': 2.26, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 2.21, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 58.03, 'Test/topk_optimal': 58.52, 'Test/topk_not_optimal': 1.48, 'Test_Qtypes/is there': 59.78, 'Test_Qtypes/is the': 58.56, 'Test_Qtypes/is this a': 57.22, 'Test_Qtypes/is there a': 59.05, 'Test_Qtypes/are the': 55.17, 'Test_Qtypes/is this': 58.24, 'Test_Qtypes/was': 54.08, 'Test_Qtypes/is': 56.23, 'Test_Qtypes/is it': 54.14, 'Test_Qtypes/are these': 57.64, 'Test_Qtypes/are there': 65.36, 'Test_Qtypes/is this an': 60.0, 'Test_Qtypes/are': 60.89, 'Test_Qtypes/is that a': 52.4, 'Test_Qtypes/are there any': 58.89, 'Test_Atypes/yes_no': 61.88, 'Test_Atypes/other': 8.8, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 66.53, 'Test/topk_optimal': 66.78, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 67.04, 'Test_Qtypes/does the': 65.78, 'Test_Qtypes/does this': 68.31, 'Test_Qtypes/can you': 64.57, 'Test_Qtypes/has': 60.65, 'Test_Qtypes/could': 76.72, 'Test_Qtypes/do you': 65.83, 'Test_Atypes/yes_no': 68.24, 'Test_Atypes/other': 1.11, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 0.27, 'Test/topk_optimal': 0.27, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.31, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.27, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 44.62, 'Test/topk_optimal': 45.75, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 22.62, 'Test_Qtypes/are they': 60.18, 'Test_Qtypes/is this person': 59.62, 'Test_Qtypes/is he': 60.81, 'Test_Qtypes/what is the woman': 19.38, 'Test_Qtypes/is the man': 55.28, 'Test_Qtypes/is this': 38.7, 'Test_Qtypes/is the person': 53.42, 'Test_Qtypes/is the woman': 55.76, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 24.0, 'Test_Qtypes/are the': 51.11, 'Test_Atypes/other': 21.8, 'Test_Atypes/yes_no': 59.7, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.03, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.04, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.0, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.03}
q_type {'Test/overall': 3.98, 'Test/topk_optimal': 4.39, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what type of': 4.37, 'Test_Qtypes/what kind of': 3.7, 'Test_Atypes/other': 3.98, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 26.71, 'Test/topk_optimal': 29.27, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 37.35, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 14.31, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 8.85, 'Test_Atypes/number': 1.72, 'Test_Atypes/other': 5.27, 'Test_Atypes/yes_no': 71.07}
q_causal {'Test/overall': 9.8, 'Test/topk_optimal': 10.32, 'Test/topk_not_optimal': 8.0, 'Test_Qtypes/why': 10.6, 'Test_Qtypes/why is the': 7.35, 'Test_Atypes/other': 9.7, 'Test_Atypes/number': 30.0}
#------------------ Final Performance --------------------#
{'q_recognition': 6.06, 'q_location': 2.21, 'q_judge': 58.03, 'q_commonsense': 66.53, 'q_count': 0.27, 'q_action': 44.62, 'q_color': 0.03, 'q_type': 3.98, 'q_subcategory': 26.71, 'q_causal': 9.8}
AP: 21.824
q_recognition {'Test/overall': 1.64, 'Test/topk_optimal': 1.82, 'Test/topk_not_optimal': 0.53, 'Test_Qtypes/what': 2.24, 'Test_Qtypes/what is the': 2.17, 'Test_Qtypes/what is on the': 1.3, 'Test_Qtypes/what is': 0.22, 'Test_Qtypes/what is this': 0.15, 'Test_Qtypes/what are the': 1.82, 'Test_Qtypes/what are': 0.52, 'Test_Qtypes/what is in the': 0.83, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 3.43, 'Test_Qtypes/what does the': 0.68, 'Test_Atypes/other': 1.7, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 0.1, 'Test/topk_optimal': 0.11, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.0, 'Test_Qtypes/where is the': 0.15, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.1, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.09, 'Test/topk_optimal': 0.09, 'Test/topk_not_optimal': 0.49, 'Test_Qtypes/is there': 0.0, 'Test_Qtypes/is the': 0.13, 'Test_Qtypes/is this a': 0.11, 'Test_Qtypes/is there a': 0.12, 'Test_Qtypes/are the': 0.1, 'Test_Qtypes/is this': 0.1, 'Test_Qtypes/was': 0.31, 'Test_Qtypes/is': 0.0, 'Test_Qtypes/is it': 0.07, 'Test_Qtypes/are these': 0.09, 'Test_Qtypes/are there': 0.0, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.07, 'Test_Atypes/other': 0.37, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.08, 'Test/topk_optimal': 0.08, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.38, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.12, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.06, 'Test_Atypes/other': 1.11, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.03, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.04, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.03, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 0.16, 'Test/topk_optimal': 0.14, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 0.0, 'Test_Qtypes/are they': 0.0, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 0.62, 'Test_Qtypes/is the man': 0.1, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 1.27, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 0.3, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 0.35, 'Test_Atypes/yes_no': 0.04, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 59.81, 'Test/topk_optimal': 60.3, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 60.13, 'Test_Qtypes/what color': 58.87, 'Test_Qtypes/what color are the': 56.13, 'Test_Qtypes/what is the color of the': 66.47, 'Test_Qtypes/what color is': 61.9, 'Test_Atypes/other': 59.81}
q_type {'Test/overall': 3.53, 'Test/topk_optimal': 3.68, 'Test/topk_not_optimal': 2.06, 'Test_Qtypes/what type of': 2.73, 'Test_Qtypes/what kind of': 4.09, 'Test_Atypes/other': 3.53, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 0.32, 'Test/topk_optimal': 0.36, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 0.49, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 0.53, 'Test_Atypes/yes_no': 0.0}
q_causal {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 0.0, 'Test_Qtypes/why is the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 1.64, 'q_location': 0.1, 'q_judge': 0.09, 'q_commonsense': 0.08, 'q_count': 0.03, 'q_action': 0.16, 'q_color': 59.81, 'q_type': 3.53, 'q_subcategory': 0.32, 'q_causal': 0.0}
AP: 6.576
q_recognition {'Test/overall': 8.7, 'Test/topk_optimal': 9.97, 'Test/topk_not_optimal': 0.88, 'Test_Qtypes/what': 10.8, 'Test_Qtypes/what is the': 8.28, 'Test_Qtypes/what is on the': 6.1, 'Test_Qtypes/what is': 5.08, 'Test_Qtypes/what is this': 17.81, 'Test_Qtypes/what are the': 6.58, 'Test_Qtypes/what are': 13.31, 'Test_Qtypes/what is in the': 10.46, 'Test_Qtypes/who is': 0.28, 'Test_Qtypes/what is the name': 2.19, 'Test_Qtypes/which': 11.37, 'Test_Qtypes/what does the': 2.1, 'Test_Atypes/other': 8.99, 'Test_Atypes/number': 0.66, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 3.88, 'Test/topk_optimal': 4.42, 'Test/topk_not_optimal': 0.37, 'Test_Qtypes/where are the': 3.8, 'Test_Qtypes/where is the': 1.16, 'Test_Qtypes/what room is': 23.82, 'Test_Atypes/other': 3.89, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.26, 'Test/topk_optimal': 0.25, 'Test/topk_not_optimal': 1.48, 'Test_Qtypes/is there': 0.16, 'Test_Qtypes/is the': 0.34, 'Test_Qtypes/is this a': 0.38, 'Test_Qtypes/is there a': 0.2, 'Test_Qtypes/are the': 0.64, 'Test_Qtypes/is this': 0.14, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.0, 'Test_Qtypes/is it': 0.57, 'Test_Qtypes/are these': 0.09, 'Test_Qtypes/are there': 0.0, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.09, 'Test_Atypes/other': 2.52, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.2, 'Test/topk_optimal': 0.2, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.19, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.4, 'Test_Qtypes/can you': 0.33, 'Test_Qtypes/has': 0.49, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.11, 'Test_Atypes/other': 3.7, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.05, 'Test/topk_optimal': 0.05, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.05, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.05, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 3.2, 'Test/topk_optimal': 3.26, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 9.37, 'Test_Qtypes/are they': 0.96, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 3.61, 'Test_Qtypes/is the man': 0.0, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 12.0, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 7.97, 'Test_Atypes/yes_no': 0.07, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 1.85, 'Test/topk_optimal': 1.84, 'Test/topk_not_optimal': 2.5, 'Test_Qtypes/what color is the': 1.52, 'Test_Qtypes/what color': 3.05, 'Test_Qtypes/what color are the': 2.77, 'Test_Qtypes/what is the color of the': 3.41, 'Test_Qtypes/what color is': 0.92, 'Test_Atypes/other': 1.85}
q_type {'Test/overall': 25.89, 'Test/topk_optimal': 28.07, 'Test/topk_not_optimal': 4.71, 'Test_Qtypes/what type of': 26.81, 'Test_Qtypes/what kind of': 25.24, 'Test_Atypes/other': 25.91, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 9.32, 'Test/topk_optimal': 10.23, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 2.87, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 26.92, 'Test_Qtypes/what brand': 7.57, 'Test_Qtypes/what animal is': 54.87, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 15.26, 'Test_Atypes/yes_no': 0.06}
q_causal {'Test/overall': 1.75, 'Test/topk_optimal': 2.26, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 2.32, 'Test_Qtypes/why is the': 0.0, 'Test_Atypes/other': 1.76, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 8.7, 'q_location': 3.88, 'q_judge': 0.26, 'q_commonsense': 0.2, 'q_count': 0.05, 'q_action': 3.2, 'q_color': 1.85, 'q_type': 25.89, 'q_subcategory': 9.32, 'q_causal': 1.75}
AP: 5.51
q_recognition {'Test/overall': 6.14, 'Test/topk_optimal': 7.03, 'Test/topk_not_optimal': 0.65, 'Test_Qtypes/what': 7.58, 'Test_Qtypes/what is the': 5.06, 'Test_Qtypes/what is on the': 2.25, 'Test_Qtypes/what is': 5.44, 'Test_Qtypes/what is this': 11.94, 'Test_Qtypes/what are the': 6.88, 'Test_Qtypes/what are': 10.93, 'Test_Qtypes/what is in the': 5.76, 'Test_Qtypes/who is': 8.8, 'Test_Qtypes/what is the name': 0.41, 'Test_Qtypes/which': 4.17, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 6.18, 'Test_Atypes/number': 4.9, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 12.68, 'Test/topk_optimal': 14.08, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 9.18, 'Test_Qtypes/where is the': 10.98, 'Test_Qtypes/what room is': 35.09, 'Test_Atypes/other': 12.7, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 59.75, 'Test/topk_optimal': 60.26, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/is there': 59.62, 'Test_Qtypes/is the': 58.91, 'Test_Qtypes/is this a': 61.54, 'Test_Qtypes/is there a': 58.29, 'Test_Qtypes/are the': 60.21, 'Test_Qtypes/is this': 58.08, 'Test_Qtypes/was': 52.65, 'Test_Qtypes/is': 62.61, 'Test_Qtypes/is it': 60.71, 'Test_Qtypes/are these': 64.18, 'Test_Qtypes/are there': 53.3, 'Test_Qtypes/is this an': 64.08, 'Test_Qtypes/are': 66.35, 'Test_Qtypes/is that a': 56.98, 'Test_Qtypes/are there any': 59.85, 'Test_Atypes/yes_no': 63.86, 'Test_Atypes/other': 6.9, 'Test_Atypes/number': 24.0}
q_commonsense {'Test/overall': 58.42, 'Test/topk_optimal': 58.63, 'Test/topk_not_optimal': 12.0, 'Test_Qtypes/do': 54.09, 'Test_Qtypes/does the': 59.57, 'Test_Qtypes/does this': 58.75, 'Test_Qtypes/can you': 56.09, 'Test_Qtypes/has': 55.45, 'Test_Qtypes/could': 67.31, 'Test_Qtypes/do you': 60.95, 'Test_Atypes/yes_no': 59.5, 'Test_Atypes/other': 20.0, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 29.96, 'Test/topk_optimal': 30.24, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 30.46, 'Test_Qtypes/how many people are': 29.92, 'Test_Qtypes/how many people are in': 34.42, 'Test_Qtypes/what number is': 5.32, 'Test_Atypes/number': 30.21, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 41.82, 'Test/topk_optimal': 42.87, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 10.56, 'Test_Qtypes/are they': 54.34, 'Test_Qtypes/is this person': 64.49, 'Test_Qtypes/is he': 65.12, 'Test_Qtypes/what is the woman': 2.37, 'Test_Qtypes/is the man': 61.35, 'Test_Qtypes/is this': 62.17, 'Test_Qtypes/is the person': 47.85, 'Test_Qtypes/is the woman': 58.18, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 46.67, 'Test_Qtypes/what is the person': 17.3, 'Test_Qtypes/are the': 56.67, 'Test_Atypes/other': 10.26, 'Test_Atypes/yes_no': 62.62, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 19.91, 'Test/topk_optimal': 20.02, 'Test/topk_not_optimal': 10.0, 'Test_Qtypes/what color is the': 20.64, 'Test_Qtypes/what color': 16.82, 'Test_Qtypes/what color are the': 21.16, 'Test_Qtypes/what is the color of the': 12.59, 'Test_Qtypes/what color is': 17.04, 'Test_Atypes/other': 19.91}
q_type {'Test/overall': 7.48, 'Test/topk_optimal': 8.2, 'Test/topk_not_optimal': 0.59, 'Test_Qtypes/what type of': 7.38, 'Test_Qtypes/what kind of': 7.55, 'Test_Atypes/other': 7.49, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 41.35, 'Test/topk_optimal': 45.11, 'Test/topk_not_optimal': 2.86, 'Test_Qtypes/none of the above': 39.05, 'Test_Qtypes/what time': 28.54, 'Test_Qtypes/what sport is': 66.54, 'Test_Qtypes/what brand': 27.85, 'Test_Qtypes/what animal is': 58.94, 'Test_Atypes/number': 4.94, 'Test_Atypes/other': 32.16, 'Test_Atypes/yes_no': 65.17}
q_causal {'Test/overall': 6.25, 'Test/topk_optimal': 6.9, 'Test/topk_not_optimal': 4.0, 'Test_Qtypes/why': 5.83, 'Test_Qtypes/why is the': 7.55, 'Test_Atypes/other': 6.28, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 6.14, 'q_location': 12.68, 'q_judge': 59.75, 'q_commonsense': 58.42, 'q_count': 29.96, 'q_action': 41.82, 'q_color': 19.91, 'q_type': 7.48, 'q_subcategory': 41.35, 'q_causal': 6.25}
AP: 28.376
q_recognition {'Test/overall': 0.69, 'Test/topk_optimal': 0.69, 'Test/topk_not_optimal': 0.65, 'Test_Qtypes/what': 0.69, 'Test_Qtypes/what is the': 0.72, 'Test_Qtypes/what is on the': 0.26, 'Test_Qtypes/what is': 0.92, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.76, 'Test_Qtypes/what are': 1.22, 'Test_Qtypes/what is in the': 0.14, 'Test_Qtypes/who is': 0.83, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.66, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.66, 'Test_Atypes/number': 0.46, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 3.29, 'Test/topk_optimal': 3.23, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 2.66, 'Test_Qtypes/where is the': 3.92, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 3.3, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 57.38, 'Test/topk_optimal': 57.84, 'Test/topk_not_optimal': 2.95, 'Test_Qtypes/is there': 58.93, 'Test_Qtypes/is the': 56.97, 'Test_Qtypes/is this a': 56.69, 'Test_Qtypes/is there a': 58.63, 'Test_Qtypes/are the': 55.22, 'Test_Qtypes/is this': 57.77, 'Test_Qtypes/was': 53.47, 'Test_Qtypes/is': 55.8, 'Test_Qtypes/is it': 53.77, 'Test_Qtypes/are these': 57.61, 'Test_Qtypes/are there': 65.85, 'Test_Qtypes/is this an': 61.02, 'Test_Qtypes/are': 61.03, 'Test_Qtypes/is that a': 51.25, 'Test_Qtypes/are there any': 58.15, 'Test_Atypes/yes_no': 61.53, 'Test_Atypes/other': 3.88, 'Test_Atypes/number': 36.0}
q_commonsense {'Test/overall': 66.9, 'Test/topk_optimal': 67.12, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 66.6, 'Test_Qtypes/does the': 66.12, 'Test_Qtypes/does this': 69.4, 'Test_Qtypes/can you': 63.04, 'Test_Qtypes/has': 63.09, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.57, 'Test_Atypes/other': 3.33, 'Test_Atypes/number': 30.0}
q_count {'Test/overall': 0.27, 'Test/topk_optimal': 0.27, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.31, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.27, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 36.16, 'Test/topk_optimal': 37.06, 'Test/topk_not_optimal': 1.71, 'Test_Qtypes/what is the man': 0.63, 'Test_Qtypes/are they': 60.78, 'Test_Qtypes/is this person': 59.1, 'Test_Qtypes/is he': 57.24, 'Test_Qtypes/what is the woman': 1.55, 'Test_Qtypes/is the man': 54.62, 'Test_Qtypes/is this': 38.26, 'Test_Qtypes/is the person': 48.23, 'Test_Qtypes/is the woman': 53.33, 'Test_Qtypes/are': 57.14, 'Test_Qtypes/are these': 58.89, 'Test_Qtypes/what is the person': 0.3, 'Test_Qtypes/are the': 51.11, 'Test_Atypes/other': 1.22, 'Test_Atypes/yes_no': 59.17, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 0.05, 'Test/topk_optimal': 0.06, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what color is the': 0.04, 'Test_Qtypes/what color': 0.0, 'Test_Qtypes/what color are the': 0.18, 'Test_Qtypes/what is the color of the': 0.0, 'Test_Qtypes/what color is': 0.0, 'Test_Atypes/other': 0.05}
q_type {'Test/overall': 0.06, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.29, 'Test_Qtypes/what type of': 0.0, 'Test_Qtypes/what kind of': 0.09, 'Test_Atypes/other': 0.06, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 24.1, 'Test/topk_optimal': 26.4, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 36.2, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.56, 'Test_Qtypes/what animal is': 1.59, 'Test_Atypes/number': 1.72, 'Test_Atypes/other': 1.26, 'Test_Atypes/yes_no': 70.56}
q_causal {'Test/overall': 12.45, 'Test/topk_optimal': 13.55, 'Test/topk_not_optimal': 8.67, 'Test_Qtypes/why': 13.11, 'Test_Qtypes/why is the': 10.41, 'Test_Atypes/other': 12.36, 'Test_Atypes/number': 30.0}
#------------------ Final Performance --------------------#
{'q_recognition': 0.69, 'q_location': 3.29, 'q_judge': 57.38, 'q_commonsense': 66.9, 'q_count': 0.27, 'q_action': 36.16, 'q_color': 0.05, 'q_type': 0.06, 'q_subcategory': 24.1, 'q_causal': 12.45}
AP: 20.135
q_recognition {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what': 0.0, 'Test_Qtypes/what is the': 0.0, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.0, 'Test_Qtypes/what is this': 0.0, 'Test_Qtypes/what are the': 0.0, 'Test_Qtypes/what are': 0.0, 'Test_Qtypes/what is in the': 0.0, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.0, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what': 0.0, 'Test_Qtypes/what is the': 0.0, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.0, 'Test_Qtypes/what is this': 0.0, 'Test_Qtypes/what are the': 0.0, 'Test_Qtypes/what are': 0.0, 'Test_Qtypes/what is in the': 0.0, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.0, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what': 0.0, 'Test_Qtypes/what is the': 0.0, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.0, 'Test_Qtypes/what is this': 0.0, 'Test_Qtypes/what are the': 0.0, 'Test_Qtypes/what are': 0.0, 'Test_Qtypes/what is in the': 0.0, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.0, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 26.01, 'Test/topk_optimal': 29.83, 'Test/topk_not_optimal': 2.48, 'Test_Qtypes/what': 25.92, 'Test_Qtypes/what is the': 25.56, 'Test_Qtypes/what is on the': 25.11, 'Test_Qtypes/what is': 22.43, 'Test_Qtypes/what is this': 36.07, 'Test_Qtypes/what are the': 26.86, 'Test_Qtypes/what are': 34.59, 'Test_Qtypes/what is in the': 32.63, 'Test_Qtypes/who is': 17.13, 'Test_Qtypes/what is the name': 6.99, 'Test_Qtypes/which': 34.35, 'Test_Qtypes/what does the': 18.45, 'Test_Atypes/other': 26.71, 'Test_Atypes/number': 6.89, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 8.47, 'Test/topk_optimal': 9.66, 'Test/topk_not_optimal': 1.14, 'Test_Qtypes/what': 9.92, 'Test_Qtypes/what is the': 9.22, 'Test_Qtypes/what is on the': 2.16, 'Test_Qtypes/what is': 4.53, 'Test_Qtypes/what is this': 16.43, 'Test_Qtypes/what are the': 7.2, 'Test_Qtypes/what are': 10.29, 'Test_Qtypes/what is in the': 4.19, 'Test_Qtypes/who is': 1.48, 'Test_Qtypes/what is the name': 1.78, 'Test_Qtypes/which': 19.85, 'Test_Qtypes/what does the': 1.64, 'Test_Atypes/other': 8.78, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 29.64, 'Test/topk_optimal': 33.21, 'Test/topk_not_optimal': 6.59, 'Test_Qtypes/where are the': 25.38, 'Test_Qtypes/where is the': 24.82, 'Test_Qtypes/what room is': 76.73, 'Test_Atypes/other': 29.69, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 0.65, 'Test/topk_optimal': 0.67, 'Test/topk_not_optimal': 0.57, 'Test_Qtypes/what': 0.75, 'Test_Qtypes/what is the': 0.6, 'Test_Qtypes/what is on the': 0.39, 'Test_Qtypes/what is': 0.75, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.52, 'Test_Qtypes/what are': 0.35, 'Test_Qtypes/what is in the': 0.28, 'Test_Qtypes/who is': 1.11, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.4, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.61, 'Test_Atypes/number': 0.92, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 1.08, 'Test/topk_optimal': 1.02, 'Test/topk_not_optimal': 1.46, 'Test_Qtypes/where are the': 1.14, 'Test_Qtypes/where is the': 1.21, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 1.08, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 65.82, 'Test/topk_optimal': 66.32, 'Test/topk_not_optimal': 7.38, 'Test_Qtypes/is there': 63.5, 'Test_Qtypes/is the': 66.67, 'Test_Qtypes/is this a': 67.73, 'Test_Qtypes/is there a': 65.89, 'Test_Qtypes/are the': 65.88, 'Test_Qtypes/is this': 65.2, 'Test_Qtypes/was': 68.57, 'Test_Qtypes/is': 64.72, 'Test_Qtypes/is it': 69.53, 'Test_Qtypes/are these': 64.09, 'Test_Qtypes/are there': 63.43, 'Test_Qtypes/is this an': 61.73, 'Test_Qtypes/are': 64.28, 'Test_Qtypes/is that a': 61.04, 'Test_Qtypes/are there any': 60.3, 'Test_Atypes/yes_no': 67.34, 'Test_Atypes/other': 46.36, 'Test_Atypes/number': 52.0}
q_recognition {'Test/overall': 0.73, 'Test/topk_optimal': 0.74, 'Test/topk_not_optimal': 0.65, 'Test_Qtypes/what': 0.71, 'Test_Qtypes/what is the': 0.8, 'Test_Qtypes/what is on the': 0.26, 'Test_Qtypes/what is': 0.92, 'Test_Qtypes/what is this': 0.77, 'Test_Qtypes/what are the': 0.76, 'Test_Qtypes/what are': 1.22, 'Test_Qtypes/what is in the': 0.14, 'Test_Qtypes/who is': 0.83, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.03, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 0.7, 'Test_Atypes/number': 0.61, 'Test_Atypes/yes_no': 100.0}
q_location {'Test/overall': 3.34, 'Test/topk_optimal': 3.29, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 2.66, 'Test_Qtypes/where is the': 3.99, 'Test_Qtypes/what room is': 0.55, 'Test_Atypes/other': 3.34, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 60.08, 'Test/topk_optimal': 60.54, 'Test/topk_not_optimal': 6.39, 'Test_Qtypes/is there': 59.95, 'Test_Qtypes/is the': 60.24, 'Test_Qtypes/is this a': 59.74, 'Test_Qtypes/is there a': 58.77, 'Test_Qtypes/are the': 58.96, 'Test_Qtypes/is this': 61.46, 'Test_Qtypes/was': 60.71, 'Test_Qtypes/is': 58.52, 'Test_Qtypes/is it': 58.82, 'Test_Qtypes/are these': 58.55, 'Test_Qtypes/are there': 66.31, 'Test_Qtypes/is this an': 61.02, 'Test_Qtypes/are': 61.62, 'Test_Qtypes/is that a': 54.48, 'Test_Qtypes/are there any': 58.37, 'Test_Atypes/yes_no': 61.82, 'Test_Atypes/other': 37.67, 'Test_Atypes/number': 58.0}
q_commonsense {'Test/overall': 67.74, 'Test/topk_optimal': 67.85, 'Test/topk_not_optimal': 42.0, 'Test_Qtypes/do': 65.97, 'Test_Qtypes/does the': 68.32, 'Test_Qtypes/does this': 70.04, 'Test_Qtypes/can you': 63.8, 'Test_Qtypes/has': 63.66, 'Test_Qtypes/could': 75.67, 'Test_Qtypes/do you': 65.95, 'Test_Atypes/yes_no': 68.8, 'Test_Atypes/other': 25.19, 'Test_Atypes/number': 75.0}
q_recognition {'Test/overall': 0.28, 'Test/topk_optimal': 0.29, 'Test/topk_not_optimal': 0.23, 'Test_Qtypes/what': 0.65, 'Test_Qtypes/what is the': 0.15, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.0, 'Test_Qtypes/what is this': 0.0, 'Test_Qtypes/what are the': 0.15, 'Test_Qtypes/what are': 0.17, 'Test_Qtypes/what is in the': 0.0, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 0.55, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.08, 'Test_Atypes/number': 5.71, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 0.25, 'Test/topk_optimal': 0.28, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.76, 'Test_Qtypes/where is the': 0.08, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.25, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.13, 'Test/topk_optimal': 0.13, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/is there': 0.0, 'Test_Qtypes/is the': 0.02, 'Test_Qtypes/is this a': 0.0, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 0.35, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.17, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.28, 'Test_Qtypes/are there': 0.62, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.55, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 1.33, 'Test_Atypes/yes_no': 0.11, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 38.0}
q_commonsense {'Test/overall': 0.08, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 18.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.28, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 45.0}
q_count {'Test/overall': 37.15, 'Test/topk_optimal': 37.48, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 37.12, 'Test_Qtypes/how many people are': 41.22, 'Test_Qtypes/how many people are in': 49.47, 'Test_Qtypes/what number is': 3.23, 'Test_Atypes/number': 37.46, 'Test_Atypes/other': 0.0}
q_recognition {'Test/overall': 10.0, 'Test/topk_optimal': 11.42, 'Test/topk_not_optimal': 1.26, 'Test_Qtypes/what': 7.21, 'Test_Qtypes/what is the': 10.22, 'Test_Qtypes/what is on the': 16.54, 'Test_Qtypes/what is': 12.55, 'Test_Qtypes/what is this': 24.74, 'Test_Qtypes/what are the': 14.1, 'Test_Qtypes/what are': 14.77, 'Test_Qtypes/what is in the': 13.09, 'Test_Qtypes/who is': 0.28, 'Test_Qtypes/what is the name': 0.82, 'Test_Qtypes/which': 1.14, 'Test_Qtypes/what does the': 7.17, 'Test_Atypes/other': 10.36, 'Test_Atypes/number': 0.15, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 1.13, 'Test/topk_optimal': 1.13, 'Test/topk_not_optimal': 1.1, 'Test_Qtypes/where are the': 1.33, 'Test_Qtypes/where is the': 1.06, 'Test_Qtypes/what room is': 1.09, 'Test_Atypes/other': 1.13, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 64.43, 'Test/topk_optimal': 64.95, 'Test/topk_not_optimal': 4.43, 'Test_Qtypes/is there': 64.89, 'Test_Qtypes/is the': 65.31, 'Test_Qtypes/is this a': 67.17, 'Test_Qtypes/is there a': 62.08, 'Test_Qtypes/are the': 63.64, 'Test_Qtypes/is this': 62.34, 'Test_Qtypes/was': 62.96, 'Test_Qtypes/is': 66.29, 'Test_Qtypes/is it': 70.32, 'Test_Qtypes/are these': 61.95, 'Test_Qtypes/are there': 55.56, 'Test_Qtypes/is this an': 68.16, 'Test_Qtypes/are': 65.2, 'Test_Qtypes/is that a': 64.48, 'Test_Qtypes/are there any': 59.63, 'Test_Atypes/yes_no': 66.97, 'Test_Atypes/other': 31.96, 'Test_Atypes/number': 26.0}
q_commonsense {'Test/overall': 63.29, 'Test/topk_optimal': 63.39, 'Test/topk_not_optimal': 42.0, 'Test_Qtypes/do': 57.11, 'Test_Qtypes/does the': 68.47, 'Test_Qtypes/does this': 62.7, 'Test_Qtypes/can you': 50.0, 'Test_Qtypes/has': 59.19, 'Test_Qtypes/could': 72.69, 'Test_Qtypes/do you': 69.64, 'Test_Atypes/yes_no': 64.39, 'Test_Atypes/other': 18.89, 'Test_Atypes/number': 75.0}
q_count {'Test/overall': 0.2, 'Test/topk_optimal': 0.21, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.23, 'Test_Qtypes/how many people are': 0.12, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.2, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 55.26, 'Test/topk_optimal': 56.68, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 31.05, 'Test_Qtypes/are they': 65.06, 'Test_Qtypes/is this person': 68.46, 'Test_Qtypes/is he': 67.56, 'Test_Qtypes/what is the woman': 27.84, 'Test_Qtypes/is the man': 66.35, 'Test_Qtypes/is this': 82.17, 'Test_Qtypes/is the person': 74.94, 'Test_Qtypes/is the woman': 75.76, 'Test_Qtypes/are': 42.86, 'Test_Qtypes/are these': 57.78, 'Test_Qtypes/what is the person': 34.1, 'Test_Qtypes/are the': 66.11, 'Test_Atypes/other': 32.23, 'Test_Atypes/yes_no': 70.5, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 1.35, 'Test/topk_optimal': 1.48, 'Test/topk_not_optimal': 0.5, 'Test_Qtypes/what': 1.81, 'Test_Qtypes/what is the': 1.57, 'Test_Qtypes/what is on the': 0.52, 'Test_Qtypes/what is': 0.17, 'Test_Qtypes/what is this': 0.15, 'Test_Qtypes/what are the': 1.74, 'Test_Qtypes/what are': 0.52, 'Test_Qtypes/what is in the': 0.83, 'Test_Qtypes/who is': 0.0, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 4.06, 'Test_Qtypes/what does the': 0.27, 'Test_Atypes/other': 1.39, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 0.34, 'Test/topk_optimal': 0.4, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 0.0, 'Test_Qtypes/where is the': 0.53, 'Test_Qtypes/what room is': 0.0, 'Test_Atypes/other': 0.34, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.12, 'Test/topk_optimal': 0.12, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/is there': 0.27, 'Test_Qtypes/is the': 0.18, 'Test_Qtypes/is this a': 0.04, 'Test_Qtypes/is there a': 0.06, 'Test_Qtypes/are the': 0.05, 'Test_Qtypes/is this': 0.15, 'Test_Qtypes/was': 0.31, 'Test_Qtypes/is': 0.0, 'Test_Qtypes/is it': 0.32, 'Test_Qtypes/are these': 0.09, 'Test_Qtypes/are there': 0.1, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.06, 'Test_Atypes/other': 0.97, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.08, 'Test/topk_optimal': 0.08, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.38, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.12, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.06, 'Test_Atypes/other': 1.11, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.03, 'Test/topk_optimal': 0.03, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.04, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.03, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 0.18, 'Test/topk_optimal': 0.16, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 0.0, 'Test_Qtypes/are they': 0.0, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 0.62, 'Test_Qtypes/is the man': 0.1, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 2.03, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 0.0, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 0.41, 'Test_Atypes/yes_no': 0.04, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 53.05, 'Test/topk_optimal': 53.42, 'Test/topk_not_optimal': 20.0, 'Test_Qtypes/what color is the': 54.07, 'Test_Qtypes/what color': 41.06, 'Test_Qtypes/what color are the': 48.57, 'Test_Qtypes/what is the color of the': 63.53, 'Test_Qtypes/what color is': 59.23, 'Test_Atypes/other': 53.05}
q_recognition {'Test/overall': 13.6, 'Test/topk_optimal': 15.63, 'Test/topk_not_optimal': 1.11, 'Test_Qtypes/what': 16.89, 'Test_Qtypes/what is the': 12.95, 'Test_Qtypes/what is on the': 7.75, 'Test_Qtypes/what is': 8.0, 'Test_Qtypes/what is this': 25.51, 'Test_Qtypes/what are the': 10.81, 'Test_Qtypes/what are': 19.13, 'Test_Qtypes/what is in the': 13.13, 'Test_Qtypes/who is': 2.96, 'Test_Qtypes/what is the name': 2.19, 'Test_Qtypes/which': 15.28, 'Test_Qtypes/what does the': 12.83, 'Test_Atypes/other': 14.07, 'Test_Atypes/number': 0.51, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 8.43, 'Test/topk_optimal': 9.74, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/where are the': 2.59, 'Test_Qtypes/where is the': 3.19, 'Test_Qtypes/what room is': 63.09, 'Test_Atypes/other': 8.44, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 1.27, 'Test/topk_optimal': 1.26, 'Test/topk_not_optimal': 1.97, 'Test_Qtypes/is there': 0.44, 'Test_Qtypes/is the': 1.37, 'Test_Qtypes/is this a': 1.77, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 1.81, 'Test_Qtypes/is this': 2.17, 'Test_Qtypes/was': 2.96, 'Test_Qtypes/is': 0.64, 'Test_Qtypes/is it': 1.55, 'Test_Qtypes/are these': 0.72, 'Test_Qtypes/are there': 0.75, 'Test_Qtypes/is this an': 1.33, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 1.04, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.55, 'Test_Atypes/other': 10.58, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.53, 'Test/topk_optimal': 0.53, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.38, 'Test_Qtypes/does the': 0.61, 'Test_Qtypes/does this': 0.81, 'Test_Qtypes/can you': 0.33, 'Test_Qtypes/has': 0.49, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.36, 'Test_Atypes/yes_no': 0.23, 'Test_Atypes/other': 12.22, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.14, 'Test/topk_optimal': 0.14, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.17, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.14, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 6.35, 'Test/topk_optimal': 6.49, 'Test/topk_not_optimal': 0.86, 'Test_Qtypes/what is the man': 18.39, 'Test_Qtypes/are they': 2.11, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.24, 'Test_Qtypes/what is the woman': 16.29, 'Test_Qtypes/is the man': 0.0, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 1.14, 'Test_Qtypes/is the woman': 2.02, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 12.1, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 15.76, 'Test_Atypes/yes_no': 0.19, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 2.99, 'Test/topk_optimal': 3.01, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/what color is the': 3.04, 'Test_Qtypes/what color': 3.97, 'Test_Qtypes/what color are the': 2.16, 'Test_Qtypes/what is the color of the': 5.88, 'Test_Qtypes/what color is': 1.62, 'Test_Atypes/other': 2.99}
q_type {'Test/overall': 30.35, 'Test/topk_optimal': 32.97, 'Test/topk_not_optimal': 5.0, 'Test_Qtypes/what type of': 33.37, 'Test_Qtypes/what kind of': 28.21, 'Test_Atypes/other': 30.38, 'Test_Atypes/number': 0.0}
q_recognition {'Test/overall': 20.88, 'Test/topk_optimal': 23.89, 'Test/topk_not_optimal': 2.36, 'Test_Qtypes/what': 22.16, 'Test_Qtypes/what is the': 22.7, 'Test_Qtypes/what is on the': 20.0, 'Test_Qtypes/what is': 14.12, 'Test_Qtypes/what is this': 34.18, 'Test_Qtypes/what are the': 21.38, 'Test_Qtypes/what are': 23.37, 'Test_Qtypes/what is in the': 23.46, 'Test_Qtypes/who is': 10.19, 'Test_Qtypes/what is the name': 1.37, 'Test_Qtypes/which': 25.2, 'Test_Qtypes/what does the': 12.37, 'Test_Atypes/other': 21.53, 'Test_Atypes/number': 3.06, 'Test_Atypes/yes_no': 0.0}
q_recognition {'Test/overall': 0.58, 'Test/topk_optimal': 0.66, 'Test/topk_not_optimal': 0.08, 'Test_Qtypes/what': 0.49, 'Test_Qtypes/what is the': 0.56, 'Test_Qtypes/what is on the': 0.0, 'Test_Qtypes/what is': 0.32, 'Test_Qtypes/what is this': 3.37, 'Test_Qtypes/what are the': 0.49, 'Test_Qtypes/what are': 0.0, 'Test_Qtypes/what is in the': 0.88, 'Test_Qtypes/who is': 0.93, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 1.11, 'Test_Qtypes/what does the': 0.0, 'Test_Atypes/other': 0.6, 'Test_Atypes/number': 0.0, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 17.38, 'Test/topk_optimal': 19.79, 'Test/topk_not_optimal': 1.83, 'Test_Qtypes/where are the': 16.77, 'Test_Qtypes/where is the': 13.19, 'Test_Qtypes/what room is': 49.45, 'Test_Atypes/other': 17.41, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.04, 'Test/topk_optimal': 0.04, 'Test/topk_not_optimal': 0.49, 'Test_Qtypes/is there': 0.0, 'Test_Qtypes/is the': 0.05, 'Test_Qtypes/is this a': 0.0, 'Test_Qtypes/is there a': 0.06, 'Test_Qtypes/are the': 0.05, 'Test_Qtypes/is this': 0.03, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.29, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/are there': 0.0, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.01, 'Test_Atypes/other': 0.37, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 0.0, 'Test_Qtypes/how many people are': 0.0, 'Test_Qtypes/how many people are in': 0.0, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 0.0}
q_action {'Test/overall': 0.09, 'Test/topk_optimal': 0.1, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 0.35, 'Test_Qtypes/are they': 0.0, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 0.31, 'Test_Qtypes/is the man': 0.0, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 0.0, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 0.24, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 11.42, 'Test/topk_optimal': 11.51, 'Test/topk_not_optimal': 3.75, 'Test_Qtypes/what color is the': 11.9, 'Test_Qtypes/what color': 11.19, 'Test_Qtypes/what color are the': 9.97, 'Test_Qtypes/what is the color of the': 15.53, 'Test_Qtypes/what color is': 7.54, 'Test_Atypes/other': 11.42}
q_type {'Test/overall': 1.76, 'Test/topk_optimal': 1.91, 'Test/topk_not_optimal': 0.29, 'Test_Qtypes/what type of': 2.0, 'Test_Qtypes/what kind of': 1.6, 'Test_Atypes/other': 1.76, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 1.79, 'Test/topk_optimal': 1.96, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/none of the above': 2.7, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 0.0, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 0.0, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 2.93, 'Test_Atypes/yes_no': 0.0}
q_causal {'Test/overall': 0.75, 'Test/topk_optimal': 0.97, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 0.99, 'Test_Qtypes/why is the': 0.0, 'Test_Atypes/other': 0.75, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 0.58, 'q_location': 17.38, 'q_judge': 0.04, 'q_commonsense': 0.0, 'q_count': 0.0, 'q_action': 0.09, 'q_color': 11.42, 'q_type': 1.76, 'q_subcategory': 1.79, 'q_causal': 0.75}
AP: 3.381
q_recognition {'Test/overall': 5.47, 'Test/topk_optimal': 6.26, 'Test/topk_not_optimal': 0.61, 'Test_Qtypes/what': 5.24, 'Test_Qtypes/what is the': 5.43, 'Test_Qtypes/what is on the': 3.94, 'Test_Qtypes/what is': 5.37, 'Test_Qtypes/what is this': 7.24, 'Test_Qtypes/what are the': 5.33, 'Test_Qtypes/what are': 8.72, 'Test_Qtypes/what is in the': 7.74, 'Test_Qtypes/who is': 0.56, 'Test_Qtypes/what is the name': 0.0, 'Test_Qtypes/which': 9.74, 'Test_Qtypes/what does the': 2.28, 'Test_Atypes/other': 5.63, 'Test_Atypes/number': 1.12, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 22.11, 'Test/topk_optimal': 24.97, 'Test/topk_not_optimal': 3.66, 'Test_Qtypes/where are the': 20.06, 'Test_Qtypes/where is the': 19.92, 'Test_Qtypes/what room is': 43.82, 'Test_Atypes/other': 22.15, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.13, 'Test/topk_optimal': 0.13, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/is there': 0.08, 'Test_Qtypes/is the': 0.17, 'Test_Qtypes/is this a': 0.34, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 0.05, 'Test_Qtypes/is this': 0.07, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.29, 'Test_Qtypes/is it': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/are there': 0.0, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/is that a': 1.35, 'Test_Qtypes/are there any': 0.0, 'Test_Atypes/yes_no': 0.03, 'Test_Atypes/other': 1.45, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.0, 'Test/topk_optimal': 0.0, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.0, 'Test_Qtypes/does the': 0.0, 'Test_Qtypes/does this': 0.0, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.0, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/other': 0.0, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 10.13, 'Test/topk_optimal': 10.22, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/how many': 10.23, 'Test_Qtypes/how many people are': 10.57, 'Test_Qtypes/how many people are in': 13.16, 'Test_Qtypes/what number is': 0.0, 'Test_Atypes/number': 10.2, 'Test_Atypes/other': 1.36}
q_action {'Test/overall': 2.08, 'Test/topk_optimal': 2.14, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 5.87, 'Test_Qtypes/are they': 0.0, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 4.43, 'Test_Qtypes/is the man': 0.0, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 7.5, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 5.27, 'Test_Atypes/yes_no': 0.0, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 24.36, 'Test/topk_optimal': 24.49, 'Test/topk_not_optimal': 12.5, 'Test_Qtypes/what color is the': 25.42, 'Test_Qtypes/what color': 21.39, 'Test_Qtypes/what color are the': 20.34, 'Test_Qtypes/what is the color of the': 34.94, 'Test_Qtypes/what color is': 19.3, 'Test_Atypes/other': 24.36}
q_type {'Test/overall': 7.13, 'Test/topk_optimal': 7.65, 'Test/topk_not_optimal': 2.06, 'Test_Qtypes/what type of': 7.25, 'Test_Qtypes/what kind of': 7.04, 'Test_Atypes/other': 7.13, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 6.36, 'Test/topk_optimal': 6.91, 'Test/topk_not_optimal': 0.71, 'Test_Qtypes/none of the above': 4.36, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 32.54, 'Test_Qtypes/what brand': 0.0, 'Test_Qtypes/what animal is': 6.11, 'Test_Atypes/number': 2.41, 'Test_Atypes/other': 10.19, 'Test_Atypes/yes_no': 0.0}
q_causal {'Test/overall': 1.2, 'Test/topk_optimal': 1.55, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 1.59, 'Test_Qtypes/why is the': 0.0, 'Test_Atypes/other': 1.21, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 5.47, 'q_location': 22.11, 'q_judge': 0.13, 'q_commonsense': 0.0, 'q_count': 10.13, 'q_action': 2.08, 'q_color': 24.36, 'q_type': 7.13, 'q_subcategory': 6.36, 'q_causal': 1.2}
AP: 7.897
q_recognition {'Test/overall': 8.3, 'Test/topk_optimal': 9.47, 'Test/topk_not_optimal': 1.11, 'Test_Qtypes/what': 6.98, 'Test_Qtypes/what is the': 9.45, 'Test_Qtypes/what is on the': 14.98, 'Test_Qtypes/what is': 6.86, 'Test_Qtypes/what is this': 11.17, 'Test_Qtypes/what are the': 9.66, 'Test_Qtypes/what are': 8.72, 'Test_Qtypes/what is in the': 14.06, 'Test_Qtypes/who is': 1.39, 'Test_Qtypes/what is the name': 1.64, 'Test_Qtypes/which': 8.01, 'Test_Qtypes/what does the': 5.07, 'Test_Atypes/other': 8.59, 'Test_Atypes/number': 0.46, 'Test_Atypes/yes_no': 0.0}
q_location {'Test/overall': 20.97, 'Test/topk_optimal': 23.48, 'Test/topk_not_optimal': 4.76, 'Test_Qtypes/where are the': 18.04, 'Test_Qtypes/where is the': 18.57, 'Test_Qtypes/what room is': 46.73, 'Test_Atypes/other': 21.0, 'Test_Atypes/number': 0.0}
q_judge {'Test/overall': 0.2, 'Test/topk_optimal': 0.19, 'Test/topk_not_optimal': 0.49, 'Test_Qtypes/is there': 0.44, 'Test_Qtypes/is the': 0.16, 'Test_Qtypes/is this a': 0.26, 'Test_Qtypes/is there a': 0.0, 'Test_Qtypes/are the': 0.1, 'Test_Qtypes/is this': 0.18, 'Test_Qtypes/was': 0.0, 'Test_Qtypes/is': 0.46, 'Test_Qtypes/is it': 0.25, 'Test_Qtypes/are these': 0.5, 'Test_Qtypes/are there': 0.0, 'Test_Qtypes/is this an': 0.0, 'Test_Qtypes/are': 0.22, 'Test_Qtypes/is that a': 0.0, 'Test_Qtypes/are there any': 0.22, 'Test_Atypes/yes_no': 0.08, 'Test_Atypes/other': 1.69, 'Test_Atypes/number': 0.0}
q_commonsense {'Test/overall': 0.32, 'Test/topk_optimal': 0.32, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/do': 0.19, 'Test_Qtypes/does the': 0.4, 'Test_Qtypes/does this': 0.65, 'Test_Qtypes/can you': 0.0, 'Test_Qtypes/has': 0.24, 'Test_Qtypes/could': 0.0, 'Test_Qtypes/do you': 0.0, 'Test_Atypes/yes_no': 0.14, 'Test_Atypes/other': 7.41, 'Test_Atypes/number': 0.0}
q_count {'Test/overall': 10.05, 'Test/topk_optimal': 10.13, 'Test/topk_not_optimal': 1.25, 'Test_Qtypes/how many': 10.51, 'Test_Qtypes/how many people are': 10.2, 'Test_Qtypes/how many people are in': 4.84, 'Test_Qtypes/what number is': 0.97, 'Test_Atypes/number': 10.11, 'Test_Atypes/other': 2.73}
q_action {'Test/overall': 4.44, 'Test/topk_optimal': 4.55, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/what is the man': 11.57, 'Test_Qtypes/are they': 0.36, 'Test_Qtypes/is this person': 0.0, 'Test_Qtypes/is he': 0.0, 'Test_Qtypes/what is the woman': 13.2, 'Test_Qtypes/is the man': 0.0, 'Test_Qtypes/is this': 0.0, 'Test_Qtypes/is the person': 0.0, 'Test_Qtypes/is the woman': 0.0, 'Test_Qtypes/are': 0.0, 'Test_Qtypes/are these': 0.0, 'Test_Qtypes/what is the person': 14.4, 'Test_Qtypes/are the': 0.0, 'Test_Atypes/other': 11.1, 'Test_Atypes/yes_no': 0.07, 'Test_Atypes/number': 0.0}
q_color {'Test/overall': 20.95, 'Test/topk_optimal': 21.01, 'Test/topk_not_optimal': 15.0, 'Test_Qtypes/what color is the': 21.67, 'Test_Qtypes/what color': 15.5, 'Test_Qtypes/what color are the': 16.77, 'Test_Qtypes/what is the color of the': 35.06, 'Test_Qtypes/what color is': 20.42, 'Test_Atypes/other': 20.95}
q_type {'Test/overall': 9.86, 'Test/topk_optimal': 10.76, 'Test/topk_not_optimal': 1.18, 'Test_Qtypes/what type of': 10.13, 'Test_Qtypes/what kind of': 9.67, 'Test_Atypes/other': 9.87, 'Test_Atypes/number': 0.0}
q_subcategory {'Test/overall': 7.61, 'Test/topk_optimal': 8.31, 'Test/topk_not_optimal': 0.48, 'Test_Qtypes/none of the above': 4.98, 'Test_Qtypes/what time': 0.0, 'Test_Qtypes/what sport is': 35.08, 'Test_Qtypes/what brand': 0.28, 'Test_Qtypes/what animal is': 13.54, 'Test_Atypes/number': 0.0, 'Test_Atypes/other': 12.42, 'Test_Atypes/yes_no': 0.13}
q_causal {'Test/overall': 2.3, 'Test/topk_optimal': 2.97, 'Test/topk_not_optimal': 0.0, 'Test_Qtypes/why': 2.45, 'Test_Qtypes/why is the': 1.84, 'Test_Atypes/other': 2.31, 'Test_Atypes/number': 0.0}
#------------------ Final Performance --------------------#
{'q_recognition': 8.3, 'q_location': 20.97, 'q_judge': 0.2, 'q_commonsense': 0.32, 'q_count': 10.05, 'q_action': 4.44, 'q_color': 20.95, 'q_type': 9.86, 'q_subcategory': 7.61, 'q_causal': 2.3}
AP: 8.5
#------------------ Final Performance --------------------#
{'q_recognition': 8.3, 'q_location': 20.97, 'q_judge': 0.2, 'q_commonsense': 0.32, 'q_count': 10.05, 'q_action': 4.44, 'q_color': 20.95, 'q_type': 9.86, 'q_subcategory': 7.61, 'q_causal': 2.3}
AP: 8.5
#------------------ Final Performance --------------------#
{'q_recognition': 0.63, 'q_location': 17.23, 'q_judge': 0.03, 'q_commonsense': 0.0, 'q_count': 0.0, 'q_action': 0.12, 'q_color': 0.0, 'q_type': 2.23, 'q_subcategory': 1.19, 'q_causal': 0.0}
AP: 2.143
