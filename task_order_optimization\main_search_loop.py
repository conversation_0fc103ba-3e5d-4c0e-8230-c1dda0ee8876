"""
Main Task Order Search Loop

This script orchestrates the iterative search for optimal task orders
in continual learning experiments.
"""

import numpy as np
import json
import time
import logging
from pathlib import Path
from typing import List, Dict, Any

# Import framework components
from order_search_framework import (
    ExperimentDataStore, 
    HeuristicProposer, 
    ContinualLearningAnalyzer,
    calculate_objective_score_from_summary
)

# Import the CL experiment adapter with proper path handling
from cl_experiment_adapter import run_my_actual_cl_experiment

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('task_order_search.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TaskOrderSearchManager:
    """Manages the iterative search for optimal task orders."""
    
    def __init__(self, all_tasks: List[str], objective_weights: Dict[str, float],
                 max_iterations: int = 10, orders_per_iteration: int = 3):
        """Initialize the search manager with validation."""
        
        # Validate inputs
        if not all_tasks:
            raise ValueError("all_tasks cannot be empty")
        if not objective_weights:
            raise ValueError("objective_weights cannot be empty")
        if max_iterations <= 0:
            raise ValueError("max_iterations must be positive")
        
        self.all_tasks = all_tasks
        self.objective_weights = objective_weights
        self.max_iterations = max_iterations
        self.orders_per_iteration = orders_per_iteration
        
        # Initialize components
        self.data_store = ExperimentDataStore(all_tasks)
        self.proposer = HeuristicProposer(all_tasks)
        self.analyzer = ContinualLearningAnalyzer()
        
        # Results tracking
        self.search_history = []
        self.start_time = None
        
        # Verify VL-T5 environment before starting
        self._verify_environment()
        
        logger.info(f"Initialized search manager with {len(all_tasks)} tasks")
        logger.info(f"Objective weights: {objective_weights}")
        logger.info(f"Max iterations: {max_iterations}, Orders per iteration: {orders_per_iteration}")
    
    def _verify_environment(self):
        """Verify that the VL-T5 environment is properly set up."""
        
        # Find VL-T5 directory - try multiple possible locations
        current_dir = Path(__file__).parent
        possible_paths = [
            current_dir.parent / "VL-T5",  # ../VL-T5 from task_order_optimization
            current_dir / ".." / "VL-T5",  # Same as above, different syntax
            Path("VL-T5"),                # Current working directory
            Path("../VL-T5"),             # One level up from current directory
        ]
        
        vl_t5_found = False
        for path in possible_paths:
            if path.exists() and path.is_dir():
                vl_t5_found = True
                logger.info(f"VL-T5 directory found: {path.resolve()}")
                break
        
        if not vl_t5_found:
            logger.warning(f"VL-T5 directory not found. Tried: {[str(p) for p in possible_paths]}")
            logger.warning("Search will use placeholder experiments for testing")
    
    def run_search(self, initial_orders: List[List[str]] = None) -> Dict[str, Any]:
        """
        Run the iterative task order search.
        
        Args:
            initial_orders: Optional list of initial task orders to evaluate
            
        Returns:
            Dictionary containing search results and statistics
        """
        
        self.start_time = time.time()
        logger.info("=" * 80)
        logger.info("STARTING TASK ORDER OPTIMIZATION SEARCH")
        logger.info("=" * 80)
        
        try:
            # Step 1: Evaluate initial orders if provided
            if initial_orders:
                logger.info(f"Evaluating {len(initial_orders)} initial orders...")
                self._evaluate_orders(initial_orders, iteration=0, is_initial=True)
            
            # Step 2: Main search loop
            for iteration in range(1, self.max_iterations + 1):
                logger.info(f"\n{'='*20} ITERATION {iteration} {'='*20}")
                
                # Propose new orders
                proposed_orders = self.proposer.propose_orders(
                    self.data_store, self.orders_per_iteration
                )
                
                logger.info(f"Proposed {len(proposed_orders)} new orders for evaluation")
                
                # Filter out orders we've already tried
                new_orders = self._filter_duplicate_orders(proposed_orders)
                
                if not new_orders:
                    logger.info("No new orders to evaluate, stopping search")
                    break
                
                # Evaluate new orders
                self._evaluate_orders(new_orders, iteration)
                
                # Log progress
                self._log_iteration_summary(iteration)
                
                # Check for convergence or stopping criteria
                if self._should_stop_search(iteration):
                    logger.info("Stopping criteria met, ending search")
                    break
            
            # Step 3: Generate final results
            results = self._generate_final_results()
            
            logger.info("=" * 80)
            logger.info("TASK ORDER OPTIMIZATION SEARCH COMPLETED")
            logger.info("=" * 80)
            
            return results
            
        except Exception as e:
            logger.error(f"Error during search: {str(e)}")
            raise
    
    def _evaluate_orders(self, orders: List[List[str]], iteration: int, is_initial: bool = False):
        """Evaluate a list of task orders."""
        
        prefix = "Initial" if is_initial else f"Iteration {iteration}"
        
        for i, order in enumerate(orders):
            logger.info(f"\n{prefix} - Evaluating order {i+1}/{len(orders)}: {order}")
            
            try:
                # Run the CL experiment
                performance_matrix = run_my_actual_cl_experiment(
                    order, self.all_tasks
                )
                
                # Add to data store
                self.data_store.add_experiment(
                    order, performance_matrix, self.all_tasks, self.objective_weights
                )
                
                # Record in search history
                self.search_history.append({
                    'iteration': iteration,
                    'order_index': i,
                    'task_order': order,
                    'timestamp': time.time() - self.start_time,
                    'is_initial': is_initial
                })
                
            except Exception as e:
                logger.error(f"Failed to evaluate order {order}: {str(e)}")
                continue
    
    def _filter_duplicate_orders(self, proposed_orders: List[List[str]]) -> List[List[str]]:
        """Filter out orders that have already been evaluated."""
        
        existing_orders = {tuple(result.task_order) for result in self.data_store.results}
        new_orders = []
        
        for order in proposed_orders:
            if tuple(order) not in existing_orders:
                new_orders.append(order)
            else:
                logger.info(f"Skipping duplicate order: {order}")
        
        logger.info(f"Filtered {len(proposed_orders)} proposed orders to {len(new_orders)} new orders")
        return new_orders
    
    def _log_iteration_summary(self, iteration: int):
        """Log summary statistics for the current iteration."""
        
        stats = self.data_store.get_summary_stats()
        
        logger.info(f"\n--- Iteration {iteration} Summary ---")
        logger.info(f"Total experiments completed: {stats.get('num_experiments', 0)}")
        
        if 'best_score' in stats:
            logger.info(f"Best objective score so far: {stats['best_score']:.4f}")
            logger.info(f"Best order so far: {stats['best_order']}")
        
        if 'objective_scores' in stats:
            scores = stats['objective_scores']
            logger.info(f"Score statistics - Mean: {scores['mean']:.4f}, "
                       f"Std: {scores['std']:.4f}, Range: [{scores['min']:.4f}, {scores['max']:.4f}]")
    
    def _should_stop_search(self, iteration: int) -> bool:
        """Determine if the search should be stopped early."""
        
        # For now, just use the maximum iteration limit
        # In the future, could add convergence criteria, time limits, etc.
        
        return iteration >= self.max_iterations
    
    def _generate_final_results(self) -> Dict[str, Any]:
        """Generate comprehensive results summary."""
        
        end_time = time.time()
        total_time = end_time - self.start_time
        
        stats = self.data_store.get_summary_stats()
        best_result = self.data_store.get_best_result()
        
        results = {
            'search_metadata': {
                'total_time_seconds': total_time,
                'total_experiments': len(self.data_store.results),
                'iterations_completed': len(set(h['iteration'] for h in self.search_history)),
                'objective_weights': self.objective_weights,
                'all_tasks': self.all_tasks
            },
            'best_result': {
                'task_order': best_result.task_order if best_result else None,
                'objective_score': best_result.objective_score if best_result else None,
                'metrics': best_result.metrics if best_result else None,
                'performance_matrix': best_result.performance_matrix.tolist() if best_result else None
            },
            'summary_statistics': stats,
            'search_history': self.search_history,
            'all_results': [
                {
                    'task_order': result.task_order,
                    'objective_score': result.objective_score,
                    'metrics': result.metrics
                }
                for result in self.data_store.results
            ]
        }
        
        # Save results to file
        self._save_results_to_file(results)
        
        # Log final summary
        logger.info(f"\nFINAL RESULTS SUMMARY:")
        logger.info(f"Total time: {total_time:.2f} seconds")
        logger.info(f"Total experiments: {len(self.data_store.results)}")
        
        if best_result:
            logger.info(f"Best task order: {best_result.task_order}")
            logger.info(f"Best objective score: {best_result.objective_score:.4f}")
            logger.info(f"Best metrics: {best_result.metrics}")
        
        return results
    
    def _save_results_to_file(self, results: Dict[str, Any]):
        """Save results to a JSON file."""
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"task_order_search_results_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Results saved to: {filename}")
        except Exception as e:
            logger.error(f"Failed to save results to file: {e}")


def main():
    """Main function to run the task order search."""
    
    # Configuration
    ALL_TASKS = [
        'q_recognition', 'q_causal', 'q_color', 'q_commonsense', 
        'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge'
    ]
    
    # Objective weights (adjust based on your priorities)
    OBJECTIVE_WEIGHTS = {
        'final_avg_accuracy': 1.0,      # Primary goal: high final accuracy
        'avg_forgetting': -0.5,         # Minimize forgetting (negative weight)
        'avg_forward_transfer': 0.3,    # Encourage positive transfer
        'learning_curve_area': 0.2,     # Reward consistent learning
        'stability': 0.1                # Prefer stable performance
    }
    
    # Search parameters
    MAX_ITERATIONS = 5  # Reduced for initial testing
    ORDERS_PER_ITERATION = 2  # Reduced for initial testing
    
    # Initial orders to evaluate (optional)
    INITIAL_ORDERS = [
        ALL_TASKS.copy(),  # Default order
        list(reversed(ALL_TASKS)),  # Reverse order
    ]
    
    # Create and run search manager
    search_manager = TaskOrderSearchManager(
        all_tasks=ALL_TASKS,
        objective_weights=OBJECTIVE_WEIGHTS,
        max_iterations=MAX_ITERATIONS,
        orders_per_iteration=ORDERS_PER_ITERATION
    )
    
    # Run the search
    results = search_manager.run_search(initial_orders=INITIAL_ORDERS)
    
    print("\n" + "="*80)
    print("SEARCH COMPLETED!")
    print("="*80)
    
    if results['best_result']['task_order']:
        print(f"Best task order found: {results['best_result']['task_order']}")
        print(f"Best objective score: {results['best_result']['objective_score']:.4f}")
        print(f"Best metrics: {results['best_result']['metrics']}")
    else:
        print("No successful experiments completed.")


if __name__ == "__main__":
    main()
