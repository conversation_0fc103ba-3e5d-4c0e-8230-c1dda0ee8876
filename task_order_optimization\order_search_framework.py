"""
Task Order Optimization Framework for Continual Learning

This module contains the core components for systematically finding better
training orders for continual learning models through iterative search.
"""

import numpy as np
import json
import random
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ExperimentResult:
    """Stores the results of a single CL experiment with a specific task order."""
    task_order: List[str]
    performance_matrix: np.ndarray  # Shape: (num_tasks_trained, num_total_tasks)
    canonical_task_list: List[str]  # Column order for performance matrix
    metrics: Dict[str, float]  # Calculated CL metrics
    objective_score: float  # Combined score for ranking


class ContinualLearningAnalyzer:
    """Calculates various continual learning metrics from performance matrices."""
    
    def __init__(self):
        pass
    
    def calculate_metrics(self, performance_matrix: np.ndarray, task_order: List[str], 
                         canonical_task_list: List[str]) -> Dict[str, float]:
        """
        Calculate comprehensive CL metrics from a performance matrix.
        
        Args:
            performance_matrix: (num_tasks_trained, num_total_tasks) accuracy matrix
            task_order: Order tasks were trained in
            canonical_task_list: Canonical order for matrix columns
            
        Returns:
            Dictionary of calculated metrics
        """
        metrics = {}
        
        # Final Average Accuracy
        final_row = performance_matrix[-1, :]
        valid_accuracies = final_row[final_row > 0]  # Only count evaluated tasks
        metrics['final_avg_accuracy'] = np.mean(valid_accuracies) if len(valid_accuracies) > 0 else 0.0
        
        # Average Forgetting
        forgetting_scores = []
        num_tasks = len(task_order)
        
        for i in range(num_tasks - 1):  # Exclude last task (no forgetting yet)
            task_name = task_order[i]
            if task_name in canonical_task_list:
                task_col = canonical_task_list.index(task_name)
                
                # Find peak performance on this task
                peak_performance = np.max(performance_matrix[:i+2, task_col])
                
                # Final performance on this task
                final_performance = performance_matrix[-1, task_col]
                
                # Forgetting = peak - final
                forgetting = peak_performance - final_performance
                forgetting_scores.append(forgetting)
        
        metrics['avg_forgetting'] = np.mean(forgetting_scores) if forgetting_scores else 0.0
        
        # Forward Transfer (simplified)
        forward_transfer_scores = []
        for i in range(1, num_tasks):
            # Compare first-time performance vs random baseline (assume 10%)
            task_name = task_order[i]
            if task_name in canonical_task_list:
                task_col = canonical_task_list.index(task_name)
                first_performance = performance_matrix[i, task_col]
                baseline = 10.0  # Assume random baseline
                forward_transfer = first_performance - baseline
                forward_transfer_scores.append(forward_transfer)
        
        metrics['avg_forward_transfer'] = np.mean(forward_transfer_scores) if forward_transfer_scores else 0.0
        
        # Learning Curve Area (area under the accuracy curve)
        incremental_accuracies = []
        for step in range(num_tasks):
            step_accuracies = performance_matrix[step, :step+1]
            step_avg = np.mean(step_accuracies) if len(step_accuracies) > 0 else 0.0
            incremental_accuracies.append(step_avg)
        
        metrics['learning_curve_area'] = np.trapz(incremental_accuracies)
        
        # Stability (variance in final accuracies)
        metrics['stability'] = -np.var(valid_accuracies) if len(valid_accuracies) > 1 else 0.0
        
        return metrics


class BaseOrderProposer(ABC):
    """Abstract base class for task order proposers."""
    
    @abstractmethod
    def propose_orders(self, data_store: 'ExperimentDataStore', 
                      num_orders: int) -> List[List[str]]:
        """Propose new task orders to try."""
        pass


class HeuristicProposer(BaseOrderProposer):
    """Proposes task orders based on heuristics and past results."""
    
    def __init__(self, all_tasks: List[str]):
        self.all_tasks = all_tasks
        self.heuristics = [
            self._easy_to_hard_heuristic,
            self._similar_tasks_together_heuristic,
            self._random_permutation_heuristic,
            self._best_performing_first_heuristic,
        ]
    
    def propose_orders(self, data_store: 'ExperimentDataStore', 
                      num_orders: int) -> List[List[str]]:
        """Propose new task orders using various heuristics."""
        proposals = []
        
        # Use different heuristics to generate proposals
        for i in range(num_orders):
            heuristic = self.heuristics[i % len(self.heuristics)]
            order = heuristic(data_store)
            if order not in proposals:
                proposals.append(order)
        
        # Fill remaining slots with random permutations if needed
        while len(proposals) < num_orders:
            random_order = self.all_tasks.copy()
            random.shuffle(random_order)
            if random_order not in proposals:
                proposals.append(random_order)
        
        return proposals[:num_orders]
    
    def _easy_to_hard_heuristic(self, data_store: 'ExperimentDataStore') -> List[str]:
        """Order tasks from easiest to hardest based on past performance."""
        if not data_store.results:
            return self.all_tasks.copy()
        
        # Calculate average performance for each task
        task_difficulties = {}
        for result in data_store.results:
            final_row = result.performance_matrix[-1, :]
            for i, task in enumerate(result.canonical_task_list):
                if task not in task_difficulties:
                    task_difficulties[task] = []
                if final_row[i] > 0:  # Only count if task was evaluated
                    task_difficulties[task].append(final_row[i])
        
        # Sort by average performance (higher = easier)
        avg_difficulties = {task: np.mean(scores) for task, scores in task_difficulties.items() 
                           if scores}
        
        # Fill in missing tasks with default difficulty
        for task in self.all_tasks:
            if task not in avg_difficulties:
                avg_difficulties[task] = 50.0  # Default middle difficulty
        
        sorted_tasks = sorted(avg_difficulties.keys(), 
                            key=lambda x: avg_difficulties[x], reverse=True)
        return sorted_tasks
    
    def _similar_tasks_together_heuristic(self, data_store: 'ExperimentDataStore') -> List[str]:
        """Group similar tasks together based on performance correlations."""
        # For now, use a simple predefined grouping
        # In practice, you could calculate task similarity from performance data
        
        groups = [
            ['q_recognition', 'q_type', 'q_subcategory'],  # Object-related
            ['q_location', 'q_count'],  # Spatial/numerical
            ['q_color', 'q_action'],  # Visual attributes
            ['q_commonsense', 'q_causal', 'q_judge']  # Reasoning
        ]
        
        order = []
        for group in groups:
            group_tasks = [task for task in group if task in self.all_tasks]
            random.shuffle(group_tasks)  # Randomize within group
            order.extend(group_tasks)
        
        # Add any missing tasks
        for task in self.all_tasks:
            if task not in order:
                order.append(task)
        
        return order
    
    def _random_permutation_heuristic(self, data_store: 'ExperimentDataStore') -> List[str]:
        """Generate a random permutation."""
        order = self.all_tasks.copy()
        random.shuffle(order)
        return order
    
    def _best_performing_first_heuristic(self, data_store: 'ExperimentDataStore') -> List[str]:
        """Put historically best-performing tasks first."""
        if not data_store.results:
            return self.all_tasks.copy()
        
        # Find the best overall result
        best_result = max(data_store.results, key=lambda x: x.objective_score)
        
        # Use the first half of the best order, then randomize the rest
        best_order = best_result.task_order
        mid_point = len(best_order) // 2
        
        new_order = best_order[:mid_point].copy()
        remaining = [task for task in self.all_tasks if task not in new_order]
        random.shuffle(remaining)
        new_order.extend(remaining)
        
        return new_order


class ExperimentDataStore:
    """Stores and manages results from all CL experiments."""
    
    def __init__(self, all_task_names_canonical: List[str]):
        self.all_task_names_canonical = all_task_names_canonical
        self.results: List[ExperimentResult] = []
        self.analyzer = ContinualLearningAnalyzer()
    
    def add_experiment(self, task_order: List[str], performance_matrix: np.ndarray,
                      canonical_task_list: List[str], objective_weights: Dict[str, float]):
        """Add a new experiment result to the store."""
        
        # Calculate metrics
        metrics = self.analyzer.calculate_metrics(performance_matrix, task_order, canonical_task_list)
        
        # Calculate objective score
        objective_score = self._calculate_objective_score(metrics, objective_weights)
        
        # Create and store result
        result = ExperimentResult(
            task_order=task_order,
            performance_matrix=performance_matrix,
            canonical_task_list=canonical_task_list,
            metrics=metrics,
            objective_score=objective_score
        )
        
        self.results.append(result)
        
        logger.info(f"Added experiment with order {task_order}")
        logger.info(f"Metrics: {metrics}")
        logger.info(f"Objective score: {objective_score:.4f}")
    
    def _calculate_objective_score(self, metrics: Dict[str, float], 
                                 weights: Dict[str, float]) -> float:
        """Calculate weighted objective score from metrics."""
        score = 0.0
        for metric_name, weight in weights.items():
            if metric_name in metrics:
                score += weight * metrics[metric_name]
        return score
    
    def get_best_result(self) -> Optional[ExperimentResult]:
        """Get the experiment with the highest objective score."""
        if not self.results:
            return None
        return max(self.results, key=lambda x: x.objective_score)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics across all experiments."""
        if not self.results:
            return {}
        
        stats = {}
        
        # Objective scores
        scores = [r.objective_score for r in self.results]
        stats['objective_scores'] = {
            'mean': np.mean(scores),
            'std': np.std(scores),
            'min': np.min(scores),
            'max': np.max(scores)
        }
        
        # Best result info
        best = self.get_best_result()
        if best:
            stats['best_order'] = best.task_order
            stats['best_score'] = best.objective_score
            stats['best_metrics'] = best.metrics
        
        stats['num_experiments'] = len(self.results)
        
        return stats


def calculate_objective_score_from_summary(metrics: Dict[str, float], 
                                         weights: Dict[str, float]) -> float:
    """Calculate objective score from metrics and weights."""
    score = 0.0
    for metric_name, weight in weights.items():
        if metric_name in metrics:
            score += weight * metrics[metric_name]
    return score
