"""
Test Subprocess Task Order Communication

This script tests whether the task order is correctly passed to subprocesses
via environment variables.
"""

import os
import sys
import subprocess
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from Question_type import set_custom_task_order, get_task_order, reset_task_order, All_task


def test_environment_variable_communication():
    """Test if task order can be passed via environment variables."""
    
    print("=" * 80)
    print("TESTING ENVIRONMENT VARIABLE COMMUNICATION")
    print("=" * 80)
    
    # Test 1: Set custom order and check if it's read from environment
    custom_order = ['q_judge', 'q_color', 'q_recognition', 'q_location']
    
    # Set environment variable directly
    os.environ['CUSTOM_TASK_ORDER'] = ','.join(custom_order)
    
    # Get task order (should read from environment)
    retrieved_order = get_task_order()
    print(f"Custom order: {custom_order}")
    print(f"Retrieved order: {retrieved_order}")
    
    if retrieved_order == custom_order:
        print("✅ Environment variable communication works!")
        success1 = True
    else:
        print("❌ Environment variable communication failed!")
        success1 = False
    
    # Clean up
    del os.environ['CUSTOM_TASK_ORDER']
    
    # Test 2: Check that it falls back to global variable when env var is not set
    set_custom_task_order(['q_type', 'q_action'])
    fallback_order = get_task_order()
    print(f"Fallback order: {fallback_order}")
    
    if fallback_order == ['q_type', 'q_action']:
        print("✅ Fallback to global variable works!")
        success2 = True
    else:
        print("❌ Fallback to global variable failed!")
        success2 = False
    
    reset_task_order()
    
    # Test 3: Check that it falls back to default when nothing is set
    default_order = get_task_order()
    print(f"Default order: {default_order}")
    
    if default_order == All_task:
        print("✅ Default fallback works!")
        success3 = True
    else:
        print("❌ Default fallback failed!")
        success3 = False
    
    return success1 and success2 and success3


def test_subprocess_communication():
    """Test if task order is correctly passed to a subprocess."""
    
    print("\n" + "=" * 80)
    print("TESTING SUBPROCESS COMMUNICATION")
    print("=" * 80)
    
    # Create a simple test script that reads the task order
    test_script_content = '''
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Question_type import get_task_order

task_order = get_task_order()
print(f"SUBPROCESS_TASK_ORDER: {task_order}")
'''
    
    # Write the test script
    test_script_path = Path("test_subprocess_reader.py")
    with open(test_script_path, 'w') as f:
        f.write(test_script_content)
    
    try:
        # Test with custom task order
        custom_order = ['q_causal', 'q_recognition', 'q_type']
        
        # Set up environment
        env = os.environ.copy()
        env['CUSTOM_TASK_ORDER'] = ','.join(custom_order)
        
        # Run subprocess
        result = subprocess.run(
            [sys.executable, str(test_script_path)],
            capture_output=True,
            text=True,
            env=env
        )
        
        print(f"Subprocess stdout: {result.stdout}")
        print(f"Subprocess stderr: {result.stderr}")
        
        # Check if the subprocess got the correct task order
        if f"SUBPROCESS_TASK_ORDER: {custom_order}" in result.stdout:
            print("✅ Subprocess correctly received custom task order!")
            return True
        else:
            print("❌ Subprocess did NOT receive correct task order!")
            return False
            
    except Exception as e:
        print(f"❌ Error running subprocess test: {e}")
        return False
    
    finally:
        # Clean up
        if test_script_path.exists():
            test_script_path.unlink()


def test_cl_experiment_adapter():
    """Test the CL experiment adapter with the fix."""
    
    print("\n" + "=" * 80)
    print("TESTING CL EXPERIMENT ADAPTER")
    print("=" * 80)
    
    try:
        from cl_experiment_adapter import CLExperimentRunner
        
        # Create runner
        runner = CLExperimentRunner()
        
        # Test task order passing
        custom_order = ['q_color', 'q_recognition', 'q_location']
        canonical_list = All_task
        
        print(f"Testing with custom order: {custom_order}")
        
        # Set the custom task order
        set_custom_task_order(custom_order)
        
        # Check if the runner can get the task order
        from Question_type import get_task_order
        current_order = get_task_order()
        
        if current_order == custom_order:
            print("✅ CL experiment adapter can access custom task order!")
            success = True
        else:
            print("❌ CL experiment adapter cannot access custom task order!")
            success = False
        
        reset_task_order()
        return success
        
    except Exception as e:
        print(f"❌ Error testing CL experiment adapter: {e}")
        return False


def main():
    """Run all tests."""
    
    print("TESTING SUBPROCESS TASK ORDER COMMUNICATION")
    print("=" * 80)
    
    # Test 1: Environment variable communication
    success1 = test_environment_variable_communication()
    
    # Test 2: Subprocess communication
    success2 = test_subprocess_communication()
    
    # Test 3: CL experiment adapter
    success3 = test_cl_experiment_adapter()
    
    print("\n" + "=" * 80)
    print("TEST RESULTS")
    print("=" * 80)
    
    if success1:
        print("✅ Environment variable communication: PASSED")
    else:
        print("❌ Environment variable communication: FAILED")
    
    if success2:
        print("✅ Subprocess communication: PASSED")
    else:
        print("❌ Subprocess communication: FAILED")
    
    if success3:
        print("✅ CL experiment adapter: PASSED")
    else:
        print("❌ CL experiment adapter: FAILED")
    
    if success1 and success2 and success3:
        print("\n🎉 ALL TESTS PASSED!")
        print("The task order override fix should now work correctly.")
    else:
        print("\n🔧 SOME TESTS FAILED!")
        print("The task order override fix needs more work.")
    
    return success1 and success2 and success3


if __name__ == "__main__":
    main()
