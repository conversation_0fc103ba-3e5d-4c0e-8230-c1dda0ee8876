{"search_metadata": {"total_time_seconds": 224347.76328396797, "total_experiments": 23, "iterations_completed": 11, "objective_weights": {"final_avg_accuracy": 1.0, "avg_forgetting": -0.5, "avg_forward_transfer": 0.3, "learning_curve_area": 0.2, "stability": 0.1}, "all_tasks": ["q_recognition", "q_causal", "q_color", "q_commonsense", "q_subcategory", "q_location", "q_count", "q_action", "q_type", "q_judge"]}, "best_result": {"task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_action", "q_color", "q_judge", "q_commonsense", "q_causal"], "objective_score": 61.72294796825397, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -22.055555555555557, "avg_forward_transfer": 3.3744444444444444, "learning_curve_area": 356.2459642857143, "stability": -610.86356}, "performance_matrix": [[36.86, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [33.99, 19.65, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [6.46, 95.0, 73.02, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.53, 11.3, 2.88, 71.08, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [34.34, 14.55, 62.1, 69.94, 54.89, 0.0, 0.0, 0.0, 0.0, 0.0], [33.35, 12.3, 58.65, 70.93, 55.47, 38.69, 0.0, 0.0, 0.0, 0.0], [13.6, 5.3, 40.12, 62.1, 37.15, 11.42, 47.28, 0.0, 0.0, 0.0], [33.77, 11.45, 50.8, 71.64, 53.22, 25.56, 34.59, 66.84, 0.0, 0.0], [37.22, 13.05, 54.34, 68.35, 53.45, 33.44, 29.78, 65.81, 48.09, 0.0], [19.17, 11.9, 50.7, 74.92, 43.34, 9.18, 21.41, 61.68, 23.93, 78.97]]}, "summary_statistics": {"objective_scores": {"mean": 54.432604973084885, "std": 3.3619886184597285, "min": 47.8932813015873, "max": 61.72294796825397}, "best_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_action", "q_color", "q_judge", "q_commonsense", "q_causal"], "best_score": 61.72294796825397, "best_metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -22.055555555555557, "avg_forward_transfer": 3.3744444444444444, "learning_curve_area": 356.2459642857143, "stability": -610.86356}, "num_experiments": 23}, "search_history": [{"iteration": 0, "order_index": 0, "task_order": ["q_recognition", "q_causal", "q_color", "q_commonsense", "q_subcategory", "q_location", "q_count", "q_action", "q_type", "q_judge"], "timestamp": 10790.315503358841, "is_initial": true}, {"iteration": 0, "order_index": 1, "task_order": ["q_judge", "q_type", "q_action", "q_count", "q_location", "q_subcategory", "q_commonsense", "q_color", "q_causal", "q_recognition"], "timestamp": 20267.11782169342, "is_initial": true}, {"iteration": 1, "order_index": 0, "task_order": ["q_judge", "q_commonsense", "q_action", "q_color", "q_subcategory", "q_type", "q_count", "q_recognition", "q_causal", "q_location"], "timestamp": 29977.478546857834, "is_initial": false}, {"iteration": 1, "order_index": 1, "task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_action", "q_color", "q_judge", "q_commonsense", "q_causal"], "timestamp": 39384.68435335159, "is_initial": false}, {"iteration": 1, "order_index": 2, "task_order": ["q_type", "q_subcategory", "q_recognition", "q_color", "q_causal", "q_count", "q_location", "q_commonsense", "q_action", "q_judge"], "timestamp": 48745.022158145905, "is_initial": false}, {"iteration": 2, "order_index": 0, "task_order": ["q_subcategory", "q_type", "q_recognition", "q_location", "q_count", "q_color", "q_action", "q_causal", "q_commonsense", "q_judge"], "timestamp": 58117.94435095787, "is_initial": false}, {"iteration": 2, "order_index": 1, "task_order": ["q_recognition", "q_subcategory", "q_color", "q_judge", "q_location", "q_causal", "q_commonsense", "q_count", "q_type", "q_action"], "timestamp": 67490.59761071205, "is_initial": false}, {"iteration": 3, "order_index": 0, "task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_color", "q_action", "q_judge", "q_commonsense", "q_causal"], "timestamp": 80170.95102190971, "is_initial": false}, {"iteration": 3, "order_index": 1, "task_order": ["q_count", "q_color", "q_location", "q_recognition", "q_commonsense", "q_subcategory", "q_judge", "q_action", "q_type", "q_causal"], "timestamp": 89706.83440375328, "is_initial": false}, {"iteration": 4, "order_index": 0, "task_order": ["q_type", "q_subcategory", "q_recognition", "q_count", "q_location", "q_color", "q_action", "q_causal", "q_judge", "q_commonsense"], "timestamp": 99209.00641417503, "is_initial": false}, {"iteration": 4, "order_index": 1, "task_order": ["q_type", "q_subcategory", "q_count", "q_action", "q_color", "q_commonsense", "q_recognition", "q_location", "q_judge", "q_causal"], "timestamp": 109207.26569700241, "is_initial": false}, {"iteration": 5, "order_index": 0, "task_order": ["q_recognition", "q_subcategory", "q_type", "q_count", "q_location", "q_action", "q_color", "q_causal", "q_judge", "q_commonsense"], "timestamp": 119229.55701994896, "is_initial": false}, {"iteration": 5, "order_index": 1, "task_order": ["q_location", "q_commonsense", "q_count", "q_recognition", "q_subcategory", "q_action", "q_color", "q_type", "q_causal", "q_judge"], "timestamp": 128730.48299884796, "is_initial": false}, {"iteration": 6, "order_index": 0, "task_order": ["q_subcategory", "q_recognition", "q_type", "q_count", "q_location", "q_action", "q_color", "q_causal", "q_judge", "q_commonsense"], "timestamp": 138227.56279826164, "is_initial": false}, {"iteration": 6, "order_index": 1, "task_order": ["q_commonsense", "q_action", "q_type", "q_count", "q_subcategory", "q_causal", "q_location", "q_recognition", "q_color", "q_judge"], "timestamp": 147727.85226988792, "is_initial": false}, {"iteration": 7, "order_index": 0, "task_order": ["q_type", "q_subcategory", "q_recognition", "q_location", "q_count", "q_color", "q_action", "q_causal", "q_judge", "q_commonsense"], "timestamp": 157231.23360824585, "is_initial": false}, {"iteration": 7, "order_index": 1, "task_order": ["q_color", "q_location", "q_causal", "q_action", "q_count", "q_recognition", "q_subcategory", "q_commonsense", "q_judge", "q_type"], "timestamp": 167163.2963051796, "is_initial": false}, {"iteration": 8, "order_index": 0, "task_order": ["q_type", "q_subcategory", "q_recognition", "q_count", "q_location", "q_color", "q_action", "q_judge", "q_commonsense", "q_causal"], "timestamp": 176664.7140393257, "is_initial": false}, {"iteration": 8, "order_index": 1, "task_order": ["q_subcategory", "q_location", "q_color", "q_count", "q_judge", "q_commonsense", "q_type", "q_causal", "q_recognition", "q_action"], "timestamp": 186265.24090504646, "is_initial": false}, {"iteration": 9, "order_index": 0, "task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_color", "q_action", "q_judge", "q_causal", "q_commonsense"], "timestamp": 195763.29234027863, "is_initial": false}, {"iteration": 9, "order_index": 1, "task_order": ["q_subcategory", "q_location", "q_commonsense", "q_recognition", "q_type", "q_judge", "q_action", "q_count", "q_color", "q_causal"], "timestamp": 205300.28280735016, "is_initial": false}, {"iteration": 10, "order_index": 0, "task_order": ["q_subcategory", "q_type", "q_recognition", "q_location", "q_count", "q_action", "q_color", "q_causal", "q_judge", "q_commonsense"], "timestamp": 214829.7910847664, "is_initial": false}, {"iteration": 10, "order_index": 1, "task_order": ["q_action", "q_count", "q_subcategory", "q_type", "q_recognition", "q_commonsense", "q_causal", "q_color", "q_location", "q_judge"], "timestamp": 224347.76284718513, "is_initial": false}], "all_results": [{"task_order": ["q_recognition", "q_causal", "q_color", "q_commonsense", "q_subcategory", "q_location", "q_count", "q_action", "q_type", "q_judge"], "objective_score": 51.29428130158731, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 24.01111111111111, "avg_forward_transfer": 45.39, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_judge", "q_type", "q_action", "q_count", "q_location", "q_subcategory", "q_commonsense", "q_color", "q_causal", "q_recognition"], "objective_score": 55.71417019047619, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -4.690000000000004, "avg_forward_transfer": 12.287777777777777, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_judge", "q_commonsense", "q_action", "q_color", "q_subcategory", "q_type", "q_count", "q_recognition", "q_causal", "q_location"], "objective_score": 56.3861701904762, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -8.67, "avg_forward_transfer": 7.894444444444446, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_action", "q_color", "q_judge", "q_commonsense", "q_causal"], "objective_score": 61.72294796825397, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -22.055555555555557, "avg_forward_transfer": 3.3744444444444444, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_type", "q_subcategory", "q_recognition", "q_color", "q_causal", "q_count", "q_location", "q_commonsense", "q_action", "q_judge"], "objective_score": 48.79050352380953, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 12.566666666666663, "avg_forward_transfer": 17.970000000000002, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_subcategory", "q_type", "q_recognition", "q_location", "q_count", "q_color", "q_action", "q_causal", "q_commonsense", "q_judge"], "objective_score": 52.45605907936508, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 3.378888888888886, "avg_forward_transfer": 14.875555555555556, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_recognition", "q_subcategory", "q_color", "q_judge", "q_location", "q_causal", "q_commonsense", "q_count", "q_type", "q_action"], "objective_score": 52.12772574603175, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 8.56222222222222, "avg_forward_transfer": 22.42, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_color", "q_action", "q_judge", "q_commonsense", "q_causal"], "objective_score": 58.62728130158731, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -14.628888888888891, "avg_forward_transfer": 5.433333333333334, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_count", "q_color", "q_location", "q_recognition", "q_commonsense", "q_subcategory", "q_judge", "q_action", "q_type", "q_causal"], "objective_score": 56.91628130158731, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -3.548888888888891, "avg_forward_transfer": 18.19666666666667, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_type", "q_subcategory", "q_recognition", "q_count", "q_location", "q_color", "q_action", "q_causal", "q_judge", "q_commonsense"], "objective_score": 47.8932813015873, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 7.677777777777774, "avg_forward_transfer": 6.831111111111111, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_type", "q_subcategory", "q_count", "q_action", "q_color", "q_commonsense", "q_recognition", "q_location", "q_judge", "q_causal"], "objective_score": 57.52361463492064, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -9.408888888888892, "avg_forward_transfer": 10.454444444444444, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_recognition", "q_subcategory", "q_type", "q_count", "q_location", "q_action", "q_color", "q_causal", "q_judge", "q_commonsense"], "objective_score": 50.77361463492064, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 0.2511111111111085, "avg_forward_transfer": 4.054444444444444, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_location", "q_commonsense", "q_count", "q_recognition", "q_subcategory", "q_action", "q_color", "q_type", "q_causal", "q_judge"], "objective_score": 53.400947968253966, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -0.8655555555555563, "avg_forward_transfer": 10.95111111111111, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_subcategory", "q_recognition", "q_type", "q_count", "q_location", "q_action", "q_color", "q_causal", "q_judge", "q_commonsense"], "objective_score": 51.906614634920636, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 0.2511111111111085, "avg_forward_transfer": 7.831111111111111, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_commonsense", "q_action", "q_type", "q_count", "q_subcategory", "q_causal", "q_location", "q_recognition", "q_color", "q_judge"], "objective_score": 55.80750352380954, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -1.8700000000000014, "avg_forward_transfer": 17.29888888888889, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_type", "q_subcategory", "q_recognition", "q_location", "q_count", "q_color", "q_action", "q_causal", "q_judge", "q_commonsense"], "objective_score": 50.042725746031756, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": 3.378888888888886, "avg_forward_transfer": 6.831111111111111, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_color", "q_location", "q_causal", "q_action", "q_count", "q_recognition", "q_subcategory", "q_commonsense", "q_judge", "q_type"], "objective_score": 57.236836857142855, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -3.703333333333335, "avg_forward_transfer": 19.007777777777775, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_type", "q_subcategory", "q_recognition", "q_count", "q_location", "q_color", "q_action", "q_judge", "q_commonsense", "q_causal"], "objective_score": 56.69317019047619, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -10.330000000000004, "avg_forward_transfer": 6.151111111111111, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_subcategory", "q_location", "q_color", "q_count", "q_judge", "q_commonsense", "q_type", "q_causal", "q_recognition", "q_action"], "objective_score": 58.3372813015873, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -6.355555555555559, "avg_forward_transfer": 18.255555555555556, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_recognition", "q_subcategory", "q_type", "q_location", "q_count", "q_color", "q_action", "q_judge", "q_causal", "q_commonsense"], "objective_score": 54.26794796825397, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -5.395555555555559, "avg_forward_transfer": 6.291111111111111, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_subcategory", "q_location", "q_commonsense", "q_recognition", "q_type", "q_judge", "q_action", "q_count", "q_color", "q_causal"], "objective_score": 54.995947968253965, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -9.802222222222225, "avg_forward_transfer": 1.3733333333333342, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_subcategory", "q_type", "q_recognition", "q_location", "q_count", "q_action", "q_color", "q_causal", "q_judge", "q_commonsense"], "objective_score": 53.138392412698416, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -4.047777777777781, "avg_forward_transfer": 4.772222222222222, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}, {"task_order": ["q_action", "q_count", "q_subcategory", "q_type", "q_recognition", "q_commonsense", "q_causal", "q_color", "q_location", "q_judge"], "objective_score": 55.89661463492063, "metrics": {"final_avg_accuracy": 39.52, "avg_forgetting": -0.17555555555555932, "avg_forward_transfer": 20.42, "learning_curve_area": 356.2459642857143, "stability": -610.86356}}]}