"""
Continual Learning Experiment Adapter

This module provides the bridge between the task order optimization framework
and the existing VL-T5 continual learning system.
"""

import os
import sys
import subprocess
import numpy as np
import json
import time
import logging
from typing import List, Dict, Optional
from pathlib import Path

# Add the parent directory to the path to import Question_type
sys.path.append(str(Path(__file__).parent.parent))

from Question_type import set_custom_task_order, reset_task_order, convert_result_matrix_to_numpy

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CLExperimentRunner:
    """Handles execution of CL experiments with different task orders."""

    def __init__(self, vl_t5_root: str = "VL-T5", workspace_root: str = "."):
        """Initialize the experiment runner."""
        # Find VL-T5 directory - try multiple possible locations
        current_dir = Path(__file__).parent
        possible_paths = [
            current_dir.parent / "VL-T5",  # ../VL-T5 from task_order_optimization
            current_dir / ".." / "VL-T5",  # Same as above, different syntax
            Path("VL-T5"),                # Current working directory
            Path("../VL-T5"),             # One level up from current directory
        ]

        self.vl_t5_root = None
        for path in possible_paths:
            if path.exists() and path.is_dir():
                self.vl_t5_root = path.resolve()
                break

        if self.vl_t5_root is None:
            raise FileNotFoundError(f"VL-T5 directory not found. Tried: {[str(p) for p in possible_paths]}")

        self.workspace_root = Path(workspace_root)
        self.script_path = self.vl_t5_root / "scripts" / "VQACL_train1.sh"

        # Verify script path exists
        if not self.script_path.exists():
            raise FileNotFoundError(f"Training script not found: {self.script_path}")

        logger.info(f"Initialized CLExperimentRunner with VL-T5 root: {self.vl_t5_root}")

    def run_cl_experiment(self, task_order_to_run: List[str],
                         canonical_task_list_for_columns: List[str],
                         experiment_name: Optional[str] = None,
                         experiment_id: Optional[int] = None) -> Optional[np.ndarray]:
        """
        Execute a continual learning experiment for a given task order.

        Args:
            task_order_to_run: List of task names defining the training sequence
            canonical_task_list_for_columns: Fixed canonical list for matrix columns
            experiment_name: Optional name for this experiment (for logging/checkpoints)

        Returns:
            Performance matrix (num_tasks_trained, num_total_tasks) or None if failed
        """

        logger.info(f"Starting CL experiment with task order: {task_order_to_run}")

        if experiment_name is None:
            experiment_name = f"exp_{int(time.time())}"

        try:
            # Step 1: Set the custom task order
            set_custom_task_order(task_order_to_run)

            # Step 2: Clean up any previous checkpoints to ensure fresh start
            self._cleanup_checkpoints()

            # Step 3: Execute the training script
            success = self._execute_training_script(experiment_name)

            if not success:
                logger.error(f"Training script failed for order: {task_order_to_run}")
                return None

            # Step 4: Extract results and convert to performance matrix
            performance_matrix = self._extract_performance_matrix(
                task_order_to_run, canonical_task_list_for_columns
            )

            # Step 5: Save individual experiment results
            if performance_matrix is not None and experiment_id is not None:
                self._save_experiment_checkpoint(
                    experiment_id, task_order_to_run, performance_matrix,
                    canonical_task_list_for_columns
                )

            if performance_matrix is not None:
                logger.info(f"Successfully completed experiment with shape: {performance_matrix.shape}")
                logger.info(f"Final average accuracy: {np.mean(performance_matrix[-1, performance_matrix[-1, :] > 0]):.2f}%")

            return performance_matrix

        except Exception as e:
            logger.error(f"Error in CL experiment: {str(e)}")
            return None

        finally:
            # Always reset the task order when done
            reset_task_order()

    def _save_experiment_checkpoint(self, experiment_id: int, task_order: List[str],
                                   performance_matrix: np.ndarray,
                                   canonical_task_list: List[str]):
        """Save individual experiment results for later analysis."""

        try:
            # Create checkpoints directory
            checkpoint_dir = Path("task_order_optimization/experiment_checkpoints")
            checkpoint_dir.mkdir(exist_ok=True)

            # Save performance matrix
            matrix_file = checkpoint_dir / f"experiment_{experiment_id}_performance_matrix.npy"
            np.save(matrix_file, performance_matrix)

            # Save experiment metadata and metrics
            from Question_type import convert_result_matrix_to_numpy, evaluate_metric

            # Convert matrix back to result_matrix format for metric calculation
            result_matrix = {}
            for step_idx, trained_task in enumerate(task_order):
                result_matrix[trained_task] = {}
                for col_idx, eval_task in enumerate(canonical_task_list):
                    result_matrix[trained_task][eval_task] = performance_matrix[step_idx, col_idx]

            # Calculate original metrics
            metrics = evaluate_metric(result_matrix)

            # Save detailed experiment data
            experiment_data = {
                'experiment_id': experiment_id,
                'task_order': task_order,
                'canonical_task_list': canonical_task_list,
                'performance_matrix': performance_matrix.tolist(),
                'result_matrix': result_matrix,
                'original_metrics': metrics,
                'timestamp': time.time()
            }

            metadata_file = checkpoint_dir / f"experiment_{experiment_id}_data.json"
            with open(metadata_file, 'w') as f:
                json.dump(experiment_data, f, indent=2, default=str)

            # Save formatted results (like the original log output)
            log_file = checkpoint_dir / f"experiment_{experiment_id}_log.txt"
            with open(log_file, 'w') as f:
                f.write(f"Experiment {experiment_id}\n")
                f.write(f"Task Order: {' → '.join(task_order)}\n")
                f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("#------------------ result_matrix --------------------#\n")
                # Write matrix in original format
                f.write("q_recognition\tq_causal\tq_color\tq_commonsense\tq_subcategory\tq_location\tq_count\tq_action\tq_type\tq_judge\n\n")

                for step_idx in range(performance_matrix.shape[0]):
                    row_values = []
                    row_sum = 0
                    for col_idx in range(performance_matrix.shape[1]):
                        val = performance_matrix[step_idx, col_idx]
                        row_values.append(f"{val:.2f}")
                        if col_idx <= step_idx:  # Only count trained tasks for average
                            row_sum += val
                    avg = row_sum / (step_idx + 1) if step_idx >= 0 else 0
                    f.write("\t".join(row_values) + f"\tAvg: {avg:.2f}\n")

                f.write("\n#------  Metric  ------#\n")
                f.write(f"Incremental avg accuracy: {metrics['Incre_avg_acc']}\n")
                f.write(f"*** Avg accuracy *** {metrics['Avg_acc']}\n")
                f.write(f"Incremental avg forget: {metrics['Incre_avg_forget']}\n")
                f.write(f"*** Avg forget *** {metrics['Avg_forget']}\n")
                f.write(f"6Q Incremental avg accuracy: {metrics['Incre_avg_acc_6Q']}\n")
                f.write(f"*** _6Q Avg accuracy *** {metrics['Avg_acc_6Q']}\n")
                f.write(f"_6Q Incremental avg forget: {metrics['Incre_avg_forget_6Q']}\n")
                f.write(f"*** _6Q Avg forget *** {metrics['Avg_forget_6Q']}\n")

            logger.info(f"Saved experiment {experiment_id} checkpoint to {checkpoint_dir}")

        except Exception as e:
            logger.error(f"Error saving experiment checkpoint: {e}")

    def _cleanup_checkpoints(self):
        """Remove previous checkpoints to ensure clean training."""
        if not self.vl_t5_root:
            logger.warning("VL-T5 root not set, skipping checkpoint cleanup")
            return

        checkpoint_dir = self.vl_t5_root / "snap" / "checkpoint"

        if checkpoint_dir.exists():
            logger.info("Cleaning up previous checkpoints...")
            try:
                # Remove all checkpoint files
                for checkpoint_file in checkpoint_dir.glob("*.pth"):
                    checkpoint_file.unlink()
                logger.info("Checkpoint cleanup completed")
            except Exception as e:
                logger.warning(f"Error during checkpoint cleanup: {e}")

    def _execute_training_script(self, experiment_name: str) -> bool:
        """Execute the VL-T5 training script."""

        # Check if VL-T5 root is valid
        if not self.vl_t5_root:
            logger.error("VL-T5 root directory not set")
            return False

        # Change to VL-T5 directory
        original_cwd = os.getcwd()

        try:
            os.chdir(self.vl_t5_root)

            # Get the current task order to pass to subprocess
            from Question_type import get_task_order
            current_task_order = get_task_order()

            # Prepare environment variables to pass task order to subprocess
            env = os.environ.copy()
            env['CUSTOM_TASK_ORDER'] = ','.join(current_task_order)

            # Prepare the command
            cmd = ["bash", "scripts/VQACL_train1.sh", "1"]  # Use 1 GPU

            logger.info(f"Executing command: {' '.join(cmd)}")
            logger.info(f"Working directory: {os.getcwd()}")
            logger.info(f"Task order being passed: {current_task_order}")

            # Execute with timeout and capture output
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=env  # Pass environment variables
            )

            # Enhanced real-time logging with pattern detection
            output_lines = []
            epoch_count = 0
            task_count = 0

            logger.info("🚀 VL-T5 TRAINING STARTED")
            logger.info("=" * 80)

            while True:
                if process.stdout is None:
                    break
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()
                    output_lines.append(line)

                    # Enhanced logging with pattern detection
                    if "epoch" in line.lower() and "loss" in line.lower():
                        epoch_count += 1
                        logger.info(f"📊 EPOCH {epoch_count}: {line}")
                    elif "task" in line.lower() and ("training" in line.lower() or "learning" in line.lower()):
                        task_count += 1
                        logger.info(f"🎯 TASK {task_count}: {line}")
                    elif "accuracy" in line.lower() or "performance" in line.lower():
                        logger.info(f"📈 PERFORMANCE: {line}")
                    elif "error" in line.lower() or "failed" in line.lower():
                        logger.error(f"❌ ERROR: {line}")
                    elif "warning" in line.lower():
                        logger.warning(f"⚠️  WARNING: {line}")
                    elif "Using task order:" in line:
                        logger.info(f"🔄 TASK ORDER: {line}")
                    elif "result_matrix" in line or "Metric" in line:
                        logger.info(f"📋 RESULTS: {line}")
                    else:
                        # Regular output with debug level to reduce noise
                        logger.debug(f"VL-T5: {line}")

            # Wait for completion
            return_code = process.poll()

            logger.info("=" * 80)
            if return_code == 0:
                logger.info("✅ VL-T5 TRAINING COMPLETED SUCCESSFULLY")
                logger.info(f"📊 Total epochs processed: {epoch_count}")
                logger.info(f"🎯 Total tasks processed: {task_count}")
                return True
            else:
                logger.error(f"❌ VL-T5 TRAINING FAILED with return code: {return_code}")
                logger.error(f"📊 Epochs completed: {epoch_count}")
                logger.error(f"🎯 Tasks completed: {task_count}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("Training script timed out")
            try:
                process.kill()
            except:
                pass
            return False
        except Exception as e:
            logger.error(f"Error executing training script: {e}")
            return False
        finally:
            os.chdir(original_cwd)

    def _extract_performance_matrix(self, task_order_used: List[str],
                                   canonical_task_list: List[str]) -> Optional[np.ndarray]:
        """
        Extract the performance matrix from the training results.

        CRITICAL: This function ONLY uses real data from VL-T5 training.
        NO fake/simulated data is generated.
        """

        logger.info("=" * 60)
        logger.info("EXTRACTING PERFORMANCE MATRIX FROM REAL TRAINING RESULTS")
        logger.info(f"Task order used: {task_order_used}")
        logger.info(f"Canonical task list: {canonical_task_list}")
        logger.info("=" * 60)

        try:
            # Check if vl_t5_root is valid
            if not self.vl_t5_root:
                logger.error("VL-T5 root directory not set")
                return None

            # Method 1: Try to load saved numpy performance matrix directly
            np_matrix_path = self.vl_t5_root / "snap" / "checkpoint" / "performance_matrix.npy"

            if np_matrix_path.exists():
                logger.info(f"✓ Found numpy performance matrix: {np_matrix_path}")
                try:
                    performance_matrix = np.load(np_matrix_path)
                    logger.info(f"✓ Successfully loaded matrix with shape: {performance_matrix.shape}")

                    # Validate matrix dimensions
                    expected_shape = (len(task_order_used), len(canonical_task_list))
                    if performance_matrix.shape == expected_shape:
                        logger.info(f"✓ Matrix shape matches expected: {expected_shape}")
                        return performance_matrix
                    else:
                        logger.warning(f"⚠ Matrix shape mismatch: got {performance_matrix.shape}, expected {expected_shape}")
                        # Try to use it anyway if it's reasonable
                        if performance_matrix.shape[1] == len(canonical_task_list):
                            logger.info("✓ Column count matches, using available rows")
                            return performance_matrix

                except Exception as e:
                    logger.error(f"✗ Error loading numpy matrix: {e}")

            # Method 2: Try to load saved result matrix from VL-T5 output
            result_matrix_path = self.vl_t5_root / "snap" / "checkpoint" / "results_matrix.json"

            if result_matrix_path.exists():
                logger.info(f"✓ Found JSON result matrix: {result_matrix_path}")
                performance_matrix = self._load_result_matrix_from_file(
                    result_matrix_path, task_order_used, canonical_task_list
                )
                if performance_matrix is not None:
                    logger.info("✓ Successfully converted JSON result matrix to numpy format")
                    return performance_matrix
                else:
                    logger.warning("✗ Failed to convert JSON result matrix")

            # Method 3: Parse training logs to extract results
            logger.info("Attempting to parse training logs for results...")
            performance_matrix = self._parse_training_logs(task_order_used, canonical_task_list)

            if performance_matrix is not None:
                logger.info("✓ Successfully extracted results from training logs")
                return performance_matrix
            else:
                logger.warning("✗ No results found in training logs")

            # CRITICAL: NO FAKE DATA FALLBACK
            logger.error("=" * 60)
            logger.error("CRITICAL ERROR: NO REAL TRAINING RESULTS FOUND")
            logger.error("This means the VL-T5 training did not complete successfully")
            logger.error("or the results were not saved properly.")
            logger.error("REFUSING to generate fake data as per requirements.")
            logger.error("=" * 60)

            return None

        except Exception as e:
            logger.error(f"✗ Critical error extracting performance matrix: {e}")
            return None

    def _load_result_matrix_from_file(self, file_path: Path, task_order_used: List[str],
                                     canonical_task_list: List[str]) -> Optional[np.ndarray]:
        """Load and convert result matrix from saved JSON file."""

        try:
            with open(file_path, 'r') as f:
                result_matrix = json.load(f)

            logger.info(f"Loaded result matrix with {len(result_matrix)} tasks")

            # Convert to numpy format using the function from Question_type.py
            from Question_type import convert_result_matrix_to_numpy

            performance_matrix = convert_result_matrix_to_numpy(
                result_matrix, task_order_used, canonical_task_list
            )

            logger.info(f"Converted to performance matrix with shape: {performance_matrix.shape}")
            return performance_matrix

        except Exception as e:
            logger.error(f"Error loading result matrix from file: {e}")
            return None

    def _parse_training_logs(self, task_order_used: List[str],
                           canonical_task_list: List[str]) -> Optional[np.ndarray]:
        """Parse training logs to extract performance results."""

        try:
            # Check if vl_t5_root is valid
            if not self.vl_t5_root:
                logger.error("VL-T5 root directory not set")
                return None

            # Look for log files in the VL-T5 directory
            log_files = list(self.vl_t5_root.glob("*.log")) + list(self.vl_t5_root.glob("*.txt"))

            if not log_files:
                logger.warning("No log files found to parse")
                return None

            # This is a simplified parser - you may need to adapt based on actual log format
            results = {}

            for log_file in log_files:
                logger.info(f"Parsing log file: {log_file}")

                try:
                    with open(log_file, 'r') as f:
                        content = f.read()

                    # Look for result patterns in the logs
                    # This would need to be customized based on actual VL-T5 output format
                    results.update(self._extract_results_from_log_content(content))

                except Exception as e:
                    logger.warning(f"Error reading log file {log_file}: {e}")
                    continue

            if results:
                # Convert parsed results to performance matrix
                performance_matrix = self._convert_parsed_results_to_matrix(
                    results, task_order_used, canonical_task_list
                )
                return performance_matrix

            return None

        except Exception as e:
            logger.error(f"Error parsing training logs: {e}")
            return None

    def _extract_results_from_log_content(self, content: str) -> Dict[str, Dict[str, float]]:
        """Extract accuracy results from log content."""

        results = {}

        # Look for patterns like "q_recognition {'Test/overall': 75.2, ...}"
        # This is a simplified pattern - adapt based on actual log format
        import re

        # Pattern to match task results
        pattern = r"(q_\w+)\s+\{[^}]*'Test/overall':\s*([\d.]+)"
        matches = re.findall(pattern, content)

        for task, accuracy in matches:
            if task not in results:
                results[task] = {}
            # For now, assume this is the final accuracy for this task
            results[task][task] = float(accuracy)

        return results

    def _convert_parsed_results_to_matrix(self, results: Dict[str, Dict[str, float]],
                                        task_order_used: List[str],
                                        canonical_task_list: List[str]) -> np.ndarray:
        """Convert parsed results to performance matrix format."""

        num_steps = len(task_order_used)
        num_total_tasks = len(canonical_task_list)

        performance_matrix = np.zeros((num_steps, num_total_tasks))

        # This is a simplified conversion - you may need more sophisticated logic
        # based on how the actual results are structured

        for step_idx, trained_task in enumerate(task_order_used):
            if trained_task in results:
                for col_idx, eval_task in enumerate(canonical_task_list):
                    if eval_task in results[trained_task]:
                        performance_matrix[step_idx, col_idx] = results[trained_task][eval_task]

        return performance_matrix




def run_my_actual_cl_experiment(task_order_to_run: List[str],
                               canonical_task_list_for_columns: List[str],
                               experiment_id: Optional[int] = None) -> Optional[np.ndarray]:
    """
    Main interface function for the optimization framework.

    This is the function that will be called by the main search loop.

    Args:
        task_order_to_run: List of task names defining the training sequence
        canonical_task_list_for_columns: Fixed canonical list for matrix columns

    Returns:
        Performance matrix (num_tasks_trained, num_total_tasks)
    """

    logger.info("=" * 60)
    logger.info("STARTING NEW CL EXPERIMENT")
    logger.info(f"Task order: {task_order_to_run}")
    logger.info(f"Canonical task list: {canonical_task_list_for_columns}")
    logger.info("=" * 60)

    # Create the experiment runner
    runner = CLExperimentRunner()

    # Run the experiment
    performance_matrix = runner.run_cl_experiment(
        task_order_to_run,
        canonical_task_list_for_columns,
        experiment_id=experiment_id
    )

    if performance_matrix is None:
        logger.error("=" * 60)
        logger.error("EXPERIMENT FAILED - NO REAL DATA AVAILABLE")
        logger.error("This experiment will be marked as failed.")
        logger.error("NO fake data will be generated.")
        logger.error("=" * 60)
        return None

    logger.info("=" * 60)
    logger.info("CL EXPERIMENT COMPLETED")
    logger.info(f"Result matrix shape: {performance_matrix.shape}")
    logger.info("=" * 60)

    return performance_matrix


if __name__ == "__main__":
    # Test the adapter
    test_order = ['q_recognition', 'q_color', 'q_location', 'q_count']
    canonical_list = ['q_recognition', 'q_causal', 'q_color', 'q_commonsense',
                     'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge']

    result = run_my_actual_cl_experiment(test_order, canonical_list)
    if result is not None:
        print(f"Test result shape: {result.shape}")
        print(f"Test result:\n{result}")
    else:
        print("Test failed - no real data available")
