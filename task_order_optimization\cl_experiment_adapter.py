"""
Continual Learning Experiment Adapter

This module provides the bridge between the task order optimization framework
and the existing VL-T5 continual learning system.
"""

import os
import sys
import subprocess
import numpy as np
import json
import time
import logging
from typing import List, Dict, Optional
from pathlib import Path

# Add the parent directory to the path to import Question_type
sys.path.append(str(Path(__file__).parent.parent))

from Question_type import set_custom_task_order, reset_task_order, convert_result_matrix_to_numpy

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CLExperimentRunner:
    """Handles execution of CL experiments with different task orders."""

    def __init__(self, vl_t5_root: str = "VL-T5", workspace_root: str = "."):
        """Initialize the experiment runner."""
        # Find VL-T5 directory - try multiple possible locations
        current_dir = Path(__file__).parent
        possible_paths = [
            current_dir.parent / "VL-T5",  # ../VL-T5 from task_order_optimization
            current_dir / ".." / "VL-T5",  # Same as above, different syntax
            Path("VL-T5"),                # Current working directory
            Path("../VL-T5"),             # One level up from current directory
        ]

        self.vl_t5_root = None
        for path in possible_paths:
            if path.exists() and path.is_dir():
                self.vl_t5_root = path.resolve()
                break

        if self.vl_t5_root is None:
            raise FileNotFoundError(f"VL-T5 directory not found. Tried: {[str(p) for p in possible_paths]}")

        self.workspace_root = Path(workspace_root)
        self.script_path = self.vl_t5_root / "scripts" / "VQACL_train1.sh"

        # Verify script path exists
        if not self.script_path.exists():
            raise FileNotFoundError(f"Training script not found: {self.script_path}")

        logger.info(f"Initialized CLExperimentRunner with VL-T5 root: {self.vl_t5_root}")

    def run_cl_experiment(self, task_order_to_run: List[str],
                         canonical_task_list_for_columns: List[str],
                         experiment_name: Optional[str] = None) -> Optional[np.ndarray]:
        """
        Execute a continual learning experiment for a given task order.

        Args:
            task_order_to_run: List of task names defining the training sequence
            canonical_task_list_for_columns: Fixed canonical list for matrix columns
            experiment_name: Optional name for this experiment (for logging/checkpoints)

        Returns:
            Performance matrix (num_tasks_trained, num_total_tasks) or None if failed
        """

        logger.info(f"Starting CL experiment with task order: {task_order_to_run}")

        if experiment_name is None:
            experiment_name = f"exp_{int(time.time())}"

        try:
            # Step 1: Set the custom task order
            set_custom_task_order(task_order_to_run)

            # Step 2: Clean up any previous checkpoints to ensure fresh start
            self._cleanup_checkpoints()

            # Step 3: Execute the training script
            success = self._execute_training_script(experiment_name)

            if not success:
                logger.error(f"Training script failed for order: {task_order_to_run}")
                return None

            # Step 4: Extract results and convert to performance matrix
            performance_matrix = self._extract_performance_matrix(
                task_order_to_run, canonical_task_list_for_columns
            )

            if performance_matrix is not None:
                logger.info(f"Successfully completed experiment with shape: {performance_matrix.shape}")
                logger.info(f"Final average accuracy: {np.mean(performance_matrix[-1, performance_matrix[-1, :] > 0]):.2f}%")

            return performance_matrix

        except Exception as e:
            logger.error(f"Error in CL experiment: {str(e)}")
            return None

        finally:
            # Always reset the task order when done
            reset_task_order()

    def _cleanup_checkpoints(self):
        """Remove previous checkpoints to ensure clean training."""
        checkpoint_dir = self.vl_t5_root / "snap" / "checkpoint"

        if checkpoint_dir.exists():
            logger.info("Cleaning up previous checkpoints...")
            try:
                # Remove all checkpoint files
                for checkpoint_file in checkpoint_dir.glob("*.pth"):
                    checkpoint_file.unlink()
                logger.info("Checkpoint cleanup completed")
            except Exception as e:
                logger.warning(f"Error during checkpoint cleanup: {e}")

    def _execute_training_script(self, experiment_name: str) -> bool:
        """Execute the VL-T5 training script."""

        # Change to VL-T5 directory
        original_cwd = os.getcwd()

        try:
            os.chdir(self.vl_t5_root)

            # Prepare the command
            cmd = ["bash", "scripts/VQACL_train1.sh", "1"]  # Use 1 GPU

            logger.info(f"Executing command: {' '.join(cmd)}")
            logger.info(f"Working directory: {os.getcwd()}")

            # Execute with timeout and capture output
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Log output in real-time
            output_lines = []
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_lines.append(output.strip())
                    logger.info(f"Training: {output.strip()}")

            # Wait for completion
            return_code = process.poll()

            if return_code == 0:
                logger.info("Training completed successfully")
                return True
            else:
                logger.error(f"Training failed with return code: {return_code}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("Training script timed out")
            process.kill()
            return False
        except Exception as e:
            logger.error(f"Error executing training script: {e}")
            return False
        finally:
            os.chdir(original_cwd)

    def _extract_performance_matrix(self, task_order_used: List[str],
                                   canonical_task_list: List[str]) -> Optional[np.ndarray]:
        """
        Extract the performance matrix from the training results.

        This function extracts the result_matrix from the VL-T5 training output
        and converts it to the format expected by the optimization framework.
        """

        try:
            # Method 1a: Try to load saved numpy performance matrix directly
            np_matrix_path = self.vl_t5_root / "snap" / "checkpoint" / "performance_matrix.npy"

            if np_matrix_path.exists():
                logger.info(f"Loading performance matrix from: {np_matrix_path}")
                try:
                    performance_matrix = np.load(np_matrix_path)
                    logger.info(f"Loaded performance matrix with shape: {performance_matrix.shape}")
                    return performance_matrix
                except Exception as e:
                    logger.warning(f"Error loading numpy matrix: {e}")

            # Method 1b: Try to load saved result matrix from VL-T5 output
            result_matrix_path = self.vl_t5_root / "snap" / "checkpoint" / "results_matrix.json"

            if result_matrix_path.exists():
                logger.info(f"Loading result matrix from: {result_matrix_path}")
                performance_matrix = self._load_result_matrix_from_file(
                    result_matrix_path, task_order_used, canonical_task_list
                )
                if performance_matrix is not None:
                    return performance_matrix

            # Method 2: Parse training logs to extract results
            logger.info("Attempting to parse training logs for results...")
            performance_matrix = self._parse_training_logs(task_order_used, canonical_task_list)

            if performance_matrix is not None:
                return performance_matrix

            # Method 3: Fallback to placeholder (for testing)
            logger.warning("Could not extract actual results, using placeholder for testing")
            performance_matrix = self._create_placeholder_matrix(task_order_used, canonical_task_list)

            return performance_matrix

        except Exception as e:
            logger.error(f"Error extracting performance matrix: {e}")
            return None

    def _load_result_matrix_from_file(self, file_path: Path, task_order_used: List[str],
                                     canonical_task_list: List[str]) -> Optional[np.ndarray]:
        """Load and convert result matrix from saved JSON file."""

        try:
            with open(file_path, 'r') as f:
                result_matrix = json.load(f)

            logger.info(f"Loaded result matrix with {len(result_matrix)} tasks")

            # Convert to numpy format using the function from Question_type.py
            from Question_type import convert_result_matrix_to_numpy

            performance_matrix = convert_result_matrix_to_numpy(
                result_matrix, task_order_used, canonical_task_list
            )

            logger.info(f"Converted to performance matrix with shape: {performance_matrix.shape}")
            return performance_matrix

        except Exception as e:
            logger.error(f"Error loading result matrix from file: {e}")
            return None

    def _parse_training_logs(self, task_order_used: List[str],
                           canonical_task_list: List[str]) -> Optional[np.ndarray]:
        """Parse training logs to extract performance results."""

        try:
            # Look for log files in the VL-T5 directory
            log_files = list(self.vl_t5_root.glob("*.log")) + list(self.vl_t5_root.glob("*.txt"))

            if not log_files:
                logger.warning("No log files found to parse")
                return None

            # This is a simplified parser - you may need to adapt based on actual log format
            results = {}

            for log_file in log_files:
                logger.info(f"Parsing log file: {log_file}")

                try:
                    with open(log_file, 'r') as f:
                        content = f.read()

                    # Look for result patterns in the logs
                    # This would need to be customized based on actual VL-T5 output format
                    results.update(self._extract_results_from_log_content(content))

                except Exception as e:
                    logger.warning(f"Error reading log file {log_file}: {e}")
                    continue

            if results:
                # Convert parsed results to performance matrix
                performance_matrix = self._convert_parsed_results_to_matrix(
                    results, task_order_used, canonical_task_list
                )
                return performance_matrix

            return None

        except Exception as e:
            logger.error(f"Error parsing training logs: {e}")
            return None

    def _extract_results_from_log_content(self, content: str) -> Dict[str, Dict[str, float]]:
        """Extract accuracy results from log content."""

        results = {}

        # Look for patterns like "q_recognition {'Test/overall': 75.2, ...}"
        # This is a simplified pattern - adapt based on actual log format
        import re

        # Pattern to match task results
        pattern = r"(q_\w+)\s+\{[^}]*'Test/overall':\s*([\d.]+)"
        matches = re.findall(pattern, content)

        for task, accuracy in matches:
            if task not in results:
                results[task] = {}
            # For now, assume this is the final accuracy for this task
            results[task][task] = float(accuracy)

        return results

    def _convert_parsed_results_to_matrix(self, results: Dict[str, Dict[str, float]],
                                        task_order_used: List[str],
                                        canonical_task_list: List[str]) -> np.ndarray:
        """Convert parsed results to performance matrix format."""

        num_steps = len(task_order_used)
        num_total_tasks = len(canonical_task_list)

        performance_matrix = np.zeros((num_steps, num_total_tasks))

        # This is a simplified conversion - you may need more sophisticated logic
        # based on how the actual results are structured

        for step_idx, trained_task in enumerate(task_order_used):
            if trained_task in results:
                for col_idx, eval_task in enumerate(canonical_task_list):
                    if eval_task in results[trained_task]:
                        performance_matrix[step_idx, col_idx] = results[trained_task][eval_task]

        return performance_matrix

    def _create_placeholder_matrix(self, task_order_used: List[str],
                                  canonical_task_list: List[str]) -> np.ndarray:
        """
        Create a placeholder performance matrix for testing.

        This should be replaced with actual result extraction from the CL training.
        """

        num_steps = len(task_order_used)
        num_total_tasks = len(canonical_task_list)

        performance_matrix = np.zeros((num_steps, num_total_tasks))

        # Create a realistic-looking performance matrix
        task_name_to_trained_step = {name: i for i, name in enumerate(task_order_used)}

        # Simulate base learning abilities for each task
        base_abilities = {
            'q_recognition': 75.0,
            'q_location': 65.0,
            'q_count': 70.0,
            'q_color': 80.0,
            'q_type': 72.0,
            'q_subcategory': 68.0,
            'q_judge': 60.0,
            'q_commonsense': 55.0,
            'q_action': 63.0,
            'q_causal': 58.0
        }

        # Simulate forgetting factors
        forgetting_factors = {task: np.random.uniform(0.85, 0.95) for task in canonical_task_list}

        for step_idx in range(num_steps):
            current_task = task_order_used[step_idx]

            for col_idx, eval_task in enumerate(canonical_task_list):
                if eval_task in task_name_to_trained_step:
                    trained_step = task_name_to_trained_step[eval_task]

                    if trained_step > step_idx:
                        # Task not trained yet
                        performance_matrix[step_idx, col_idx] = 0.0
                    elif trained_step == step_idx:
                        # Task just trained - high performance
                        base_perf = base_abilities.get(eval_task, 65.0)
                        # Add some noise
                        performance_matrix[step_idx, col_idx] = base_perf + np.random.normal(0, 3)
                    else:
                        # Task trained before - apply forgetting
                        prev_perf = performance_matrix[step_idx - 1, col_idx]
                        forget_factor = forgetting_factors[eval_task]
                        performance_matrix[step_idx, col_idx] = prev_perf * forget_factor
                else:
                    performance_matrix[step_idx, col_idx] = 0.0

        # Clip to reasonable range
        performance_matrix = np.clip(performance_matrix, 0, 100)

        logger.info(f"Created placeholder matrix with shape: {performance_matrix.shape}")
        logger.info(f"Final row (last step): {performance_matrix[-1, :]}")

        return performance_matrix


def run_my_actual_cl_experiment(task_order_to_run: List[str],
                               canonical_task_list_for_columns: List[str]) -> np.ndarray:
    """
    Main interface function for the optimization framework.

    This is the function that will be called by the main search loop.

    Args:
        task_order_to_run: List of task names defining the training sequence
        canonical_task_list_for_columns: Fixed canonical list for matrix columns

    Returns:
        Performance matrix (num_tasks_trained, num_total_tasks)
    """

    logger.info("=" * 60)
    logger.info("STARTING NEW CL EXPERIMENT")
    logger.info(f"Task order: {task_order_to_run}")
    logger.info(f"Canonical task list: {canonical_task_list_for_columns}")
    logger.info("=" * 60)

    # Create the experiment runner
    runner = CLExperimentRunner()

    # Run the experiment
    performance_matrix = runner.run_cl_experiment(
        task_order_to_run,
        canonical_task_list_for_columns
    )

    if performance_matrix is None:
        logger.error("Experiment failed, returning zero matrix")
        # Return a zero matrix as fallback
        num_steps = len(task_order_to_run)
        num_total_tasks = len(canonical_task_list_for_columns)
        performance_matrix = np.zeros((num_steps, num_total_tasks))

    logger.info("=" * 60)
    logger.info("CL EXPERIMENT COMPLETED")
    logger.info(f"Result matrix shape: {performance_matrix.shape}")
    logger.info("=" * 60)

    return performance_matrix


if __name__ == "__main__":
    # Test the adapter
    test_order = ['q_recognition', 'q_color', 'q_location', 'q_count']
    canonical_list = ['q_recognition', 'q_causal', 'q_color', 'q_commonsense',
                     'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge']

    result = run_my_actual_cl_experiment(test_order, canonical_list)
    print(f"Test result shape: {result.shape}")
    print(f"Test result:\n{result}")
