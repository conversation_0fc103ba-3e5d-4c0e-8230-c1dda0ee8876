"""
Comprehensive Analysis of Task Order Optimization Results

This script analyzes the results from all 23 experiments and presents
the metrics in a clear, readable format.
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import seaborn as sns


def load_results(results_file: str = "task_order_search_results_20250601_060319.json") -> Dict[str, Any]:
    """Load the results JSON file."""
    with open(results_file, 'r') as f:
        return json.load(f)


def create_results_dataframe(results: Dict[str, Any]) -> pd.DataFrame:
    """Create a comprehensive DataFrame with all experiment results."""
    
    all_results = results['all_results']
    search_history = results['search_history']
    
    # Create a mapping from task_order to search history info
    history_map = {}
    for hist in search_history:
        order_key = tuple(hist['task_order'])
        history_map[order_key] = {
            'iteration': hist['iteration'],
            'order_index': hist['order_index'],
            'timestamp': hist['timestamp'],
            'is_initial': hist['is_initial']
        }
    
    # Build the DataFrame
    data = []
    for i, result in enumerate(all_results):
        order_key = tuple(result['task_order'])
        hist_info = history_map.get(order_key, {})
        
        row = {
            'experiment_id': i + 1,
            'iteration': hist_info.get('iteration', -1),
            'order_index': hist_info.get('order_index', -1),
            'is_initial': hist_info.get('is_initial', False),
            'timestamp_hours': hist_info.get('timestamp', 0) / 3600,  # Convert to hours
            'task_order': ' → '.join(result['task_order']),
            'task_order_list': result['task_order'],
            'objective_score': result['objective_score'],
            'final_avg_accuracy': result['metrics']['final_avg_accuracy'],
            'avg_forgetting': result['metrics']['avg_forgetting'],
            'avg_forward_transfer': result['metrics']['avg_forward_transfer'],
            'learning_curve_area': result['metrics']['learning_curve_area'],
            'stability': result['metrics']['stability']
        }
        data.append(row)
    
    df = pd.DataFrame(data)
    
    # Sort by objective score (best first)
    df = df.sort_values('objective_score', ascending=False).reset_index(drop=True)
    df['rank'] = df.index + 1
    
    return df


def print_summary_statistics(results: Dict[str, Any], df: pd.DataFrame):
    """Print overall summary statistics."""
    
    print("=" * 80)
    print("TASK ORDER OPTIMIZATION RESULTS SUMMARY")
    print("=" * 80)
    
    metadata = results['search_metadata']
    
    print(f"Total Experiments: {metadata['total_experiments']}")
    print(f"Total Time: {metadata['total_time_seconds'] / 3600:.1f} hours ({metadata['total_time_seconds'] / 86400:.1f} days)")
    print(f"Iterations Completed: {metadata['iterations_completed']}")
    print(f"Average Time per Experiment: {metadata['total_time_seconds'] / metadata['total_experiments'] / 3600:.1f} hours")
    
    print(f"\nObjective Weights Used:")
    for metric, weight in metadata['objective_weights'].items():
        print(f"  {metric}: {weight}")
    
    print(f"\nObjective Score Statistics:")
    print(f"  Best Score: {df['objective_score'].max():.4f}")
    print(f"  Worst Score: {df['objective_score'].min():.4f}")
    print(f"  Mean Score: {df['objective_score'].mean():.4f}")
    print(f"  Std Dev: {df['objective_score'].std():.4f}")
    
    print(f"\nFinal Average Accuracy Statistics:")
    print(f"  All experiments achieved: {df['final_avg_accuracy'].iloc[0]:.2f}%")
    print(f"  (Note: All experiments had the same final accuracy)")
    
    print(f"\nAverage Forgetting Statistics:")
    print(f"  Best (lowest): {df['avg_forgetting'].min():.2f}%")
    print(f"  Worst (highest): {df['avg_forgetting'].max():.2f}%")
    print(f"  Mean: {df['avg_forgetting'].mean():.2f}%")
    
    print(f"\nAverage Forward Transfer Statistics:")
    print(f"  Best (highest): {df['avg_forward_transfer'].max():.2f}%")
    print(f"  Worst (lowest): {df['avg_forward_transfer'].min():.2f}%")
    print(f"  Mean: {df['avg_forward_transfer'].mean():.2f}%")


def print_top_results(df: pd.DataFrame, top_n: int = 10):
    """Print the top N results in detail."""
    
    print(f"\n{'=' * 80}")
    print(f"TOP {top_n} TASK ORDERS BY OBJECTIVE SCORE")
    print("=" * 80)
    
    for i in range(min(top_n, len(df))):
        row = df.iloc[i]
        
        print(f"\n#{row['rank']} - Experiment {row['experiment_id']} (Iteration {row['iteration']})")
        print(f"Objective Score: {row['objective_score']:.4f}")
        print(f"Task Order: {row['task_order']}")
        print(f"Metrics:")
        print(f"  • Final Avg Accuracy: {row['final_avg_accuracy']:.2f}%")
        print(f"  • Avg Forgetting: {row['avg_forgetting']:.2f}%")
        print(f"  • Avg Forward Transfer: {row['avg_forward_transfer']:.2f}%")
        print(f"  • Learning Curve Area: {row['learning_curve_area']:.2f}")
        print(f"  • Stability: {row['stability']:.2f}")
        print(f"Time: {row['timestamp_hours']:.1f} hours")


def print_worst_results(df: pd.DataFrame, bottom_n: int = 5):
    """Print the worst N results for comparison."""
    
    print(f"\n{'=' * 80}")
    print(f"BOTTOM {bottom_n} TASK ORDERS BY OBJECTIVE SCORE")
    print("=" * 80)
    
    for i in range(max(0, len(df) - bottom_n), len(df)):
        row = df.iloc[i]
        
        print(f"\n#{row['rank']} - Experiment {row['experiment_id']} (Iteration {row['iteration']})")
        print(f"Objective Score: {row['objective_score']:.4f}")
        print(f"Task Order: {row['task_order']}")
        print(f"Metrics:")
        print(f"  • Final Avg Accuracy: {row['final_avg_accuracy']:.2f}%")
        print(f"  • Avg Forgetting: {row['avg_forgetting']:.2f}%")
        print(f"  • Avg Forward Transfer: {row['avg_forward_transfer']:.2f}%")


def analyze_task_positions(df: pd.DataFrame):
    """Analyze which tasks perform best in which positions."""
    
    print(f"\n{'=' * 80}")
    print("TASK POSITION ANALYSIS")
    print("=" * 80)
    
    all_tasks = ['q_recognition', 'q_causal', 'q_color', 'q_commonsense', 
                'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge']
    
    # Create position analysis
    position_scores = {task: [] for task in all_tasks}
    
    for _, row in df.iterrows():
        task_order = row['task_order_list']
        obj_score = row['objective_score']
        
        for pos, task in enumerate(task_order):
            position_scores[task].append((pos, obj_score))
    
    print("Average objective score when task is in each position:")
    print("(Position 0 = first, Position 9 = last)")
    
    for task in all_tasks:
        if position_scores[task]:
            positions = [p[0] for p in position_scores[task]]
            scores = [p[1] for p in position_scores[task]]
            
            avg_position = np.mean(positions)
            avg_score_when_used = np.mean(scores)
            
            print(f"{task:15s}: Avg Position {avg_position:.1f}, Avg Score {avg_score_when_used:.2f}")


def create_metrics_comparison_table(df: pd.DataFrame):
    """Create a detailed comparison table of all experiments."""
    
    print(f"\n{'=' * 80}")
    print("DETAILED METRICS COMPARISON (ALL 23 EXPERIMENTS)")
    print("=" * 80)
    
    # Create a formatted table
    print(f"{'Rank':<4} {'Exp':<3} {'Iter':<4} {'Obj Score':<10} {'Accuracy':<8} {'Forgetting':<10} {'Transfer':<9} {'First Task':<12} {'Last Task':<12}")
    print("-" * 80)
    
    for _, row in df.iterrows():
        first_task = row['task_order_list'][0].replace('q_', '')
        last_task = row['task_order_list'][-1].replace('q_', '')
        
        print(f"{row['rank']:<4} {row['experiment_id']:<3} {row['iteration']:<4} "
              f"{row['objective_score']:<10.3f} {row['final_avg_accuracy']:<8.2f} "
              f"{row['avg_forgetting']:<10.2f} {row['avg_forward_transfer']:<9.2f} "
              f"{first_task:<12} {last_task:<12}")


def save_detailed_csv(df: pd.DataFrame, filename: str = "detailed_results.csv"):
    """Save detailed results to CSV for further analysis."""
    
    # Select and rename columns for CSV
    csv_df = df[['rank', 'experiment_id', 'iteration', 'is_initial', 'timestamp_hours',
                'objective_score', 'final_avg_accuracy', 'avg_forgetting', 
                'avg_forward_transfer', 'learning_curve_area', 'stability', 'task_order']].copy()
    
    csv_df.to_csv(filename, index=False)
    print(f"\nDetailed results saved to: {filename}")


def main():
    """Main analysis function."""
    
    # Load results
    try:
        results = load_results()
    except FileNotFoundError:
        print("Error: Results file not found. Please ensure task_order_search_results_*.json exists.")
        return
    
    # Create DataFrame
    df = create_results_dataframe(results)
    
    # Print all analyses
    print_summary_statistics(results, df)
    print_top_results(df, top_n=10)
    print_worst_results(df, bottom_n=5)
    analyze_task_positions(df)
    create_metrics_comparison_table(df)
    
    # Save detailed results
    save_detailed_csv(df)
    
    print(f"\n{'=' * 80}")
    print("ANALYSIS COMPLETE")
    print("=" * 80)
    print(f"Key Findings:")
    print(f"1. Best task order: {df.iloc[0]['task_order']}")
    print(f"2. Best objective score: {df.iloc[0]['objective_score']:.4f}")
    print(f"3. Lowest forgetting: {df['avg_forgetting'].min():.2f}%")
    print(f"4. Highest forward transfer: {df['avg_forward_transfer'].max():.2f}%")
    print(f"5. All experiments achieved the same final accuracy: {df['final_avg_accuracy'].iloc[0]:.2f}%")


if __name__ == "__main__":
    main()
