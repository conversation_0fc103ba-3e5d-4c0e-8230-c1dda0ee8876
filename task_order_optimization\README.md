# Task Order Optimization Framework

This framework provides systematic optimization of task training orders for continual learning models. It integrates with the existing VL-T5 VQA continual learning system to automatically discover better task sequences through iterative search.

## Overview

The framework consists of several key components:

1. **Order Search Framework** (`order_search_framework.py`): Core optimization components including experiment data storage, metric calculation, and heuristic-based order proposers.

2. **CL Experiment Adapter** (`cl_experiment_adapter.py`): Bridge between the optimization framework and the existing VL-T5 continual learning system.

3. **Main Search Loop** (`main_search_loop.py`): Orchestrates the iterative search process.

4. **Integration with Question_type.py**: Modified to support dynamic task order setting.

## Key Features

- **Automated Task Order Search**: Systematically proposes and evaluates different task training sequences
- **Multiple Heuristics**: Uses various strategies (easy-to-hard, similarity-based, random, etc.) to propose orders
- **Comprehensive Metrics**: Calculates forgetting, forward transfer, learning curve area, and stability
- **Configurable Objectives**: Weighted combination of multiple metrics for flexible optimization goals
- **Result Tracking**: Stores all experiment results with detailed metrics and performance matrices
- **Integration Ready**: Works with existing VL-T5 training pipeline

## Quick Start

### 1. Test the Integration

First, verify that everything is working correctly:

```bash
cd task_order_optimization
python test_integration.py
```

This will run a series of tests to ensure all components are properly integrated.

### 2. Run a Full Search

To run the complete task order optimization:

```bash
python main_search_loop.py
```

This will:
- Evaluate initial task orders (default and reverse)
- Iteratively propose and test new orders using heuristics
- Track all results and find the best performing order
- Save detailed results to a JSON file

### 3. Customize the Search

Edit `main_search_loop.py` to customize:

- **Objective weights**: Adjust the importance of different metrics
- **Search parameters**: Change number of iterations and orders per iteration
- **Initial orders**: Provide specific starting orders to evaluate

## Configuration

### Objective Weights

The framework optimizes a weighted combination of metrics:

```python
OBJECTIVE_WEIGHTS = {
    'final_avg_accuracy': 1.0,      # Primary goal: high final accuracy
    'avg_forgetting': -0.5,         # Minimize forgetting (negative weight)
    'avg_forward_transfer': 0.3,    # Encourage positive transfer
    'learning_curve_area': 0.2,     # Reward consistent learning
    'stability': 0.1                # Prefer stable performance
}
```

### Search Parameters

```python
MAX_ITERATIONS = 10          # Number of search iterations
ORDERS_PER_ITERATION = 3     # New orders to try each iteration
```

## Architecture

### Core Components

1. **ExperimentDataStore**: Manages all experiment results and calculates metrics
2. **HeuristicProposer**: Generates new task orders using various strategies
3. **ContinualLearningAnalyzer**: Computes CL metrics from performance matrices
4. **CLExperimentRunner**: Executes actual CL training with specified task orders

### Integration Points

- **Question_type.py**: Modified to support dynamic task order setting
- **VL-T5/src/vqacl2.py**: Updated to use custom task orders from Question_type.py
- **Training Script**: Uses existing `VQACL_train1.sh` for actual training

## Metrics Calculated

The framework calculates several important continual learning metrics:

- **Final Average Accuracy**: Mean accuracy across all tasks after training
- **Average Forgetting**: How much performance degrades on previously learned tasks
- **Average Forward Transfer**: Benefit from previous tasks when learning new ones
- **Learning Curve Area**: Overall learning efficiency across the sequence
- **Stability**: Consistency of performance across tasks

## Output

The search produces:

1. **Console Logs**: Real-time progress and results
2. **Log File**: Detailed execution log (`task_order_search.log`)
3. **Results JSON**: Comprehensive results including:
   - Best task order found
   - All experiment results
   - Search statistics
   - Performance matrices

## Example Results

```json
{
  "best_result": {
    "task_order": ["q_subcategory", "q_recognition", "q_color", ...],
    "objective_score": 45.67,
    "metrics": {
      "final_avg_accuracy": 72.3,
      "avg_forgetting": 8.2,
      "avg_forward_transfer": 12.1,
      ...
    }
  },
  "search_metadata": {
    "total_experiments": 25,
    "total_time_seconds": 3600,
    ...
  }
}
```

## Important Notes

### Current Implementation Status

- ✅ Framework architecture complete
- ✅ Integration with Question_type.py
- ✅ Heuristic-based order proposers
- ✅ Comprehensive metric calculation
- ⚠️ **Performance matrix extraction**: Currently uses placeholder implementation

### Performance Matrix Extraction

The current implementation uses a placeholder for extracting performance matrices from the VL-T5 training. To complete the integration, you need to:

1. Modify the VL-T5 training code to save `trainer.result_matrix` to a file
2. Update `cl_experiment_adapter.py` to read this file
3. Or capture the results directly from the training process

### Training Time Considerations

- Each CL experiment takes significant time (hours)
- Start with small search parameters for testing
- Consider running overnight or on dedicated compute resources
- The framework supports resuming from saved results

## Extending the Framework

### Adding New Heuristics

Add new order proposers by extending `BaseOrderProposer`:

```python
class MyCustomProposer(BaseOrderProposer):
    def propose_orders(self, data_store, num_orders):
        # Your custom logic here
        return proposed_orders
```

### Adding New Metrics

Extend `ContinualLearningAnalyzer.calculate_metrics()`:

```python
def calculate_metrics(self, performance_matrix, task_order, canonical_task_list):
    metrics = {}
    # ... existing metrics ...
    metrics['my_custom_metric'] = self._calculate_custom_metric(performance_matrix)
    return metrics
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running from the correct directory
2. **Path Issues**: Check that VL-T5 directory exists and is accessible
3. **Training Failures**: Verify the VL-T5 environment is properly set up
4. **Memory Issues**: Reduce batch sizes or number of parallel experiments

### Debug Mode

Enable debug logging by modifying the logging level:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

- **Parallel Execution**: Run multiple experiments simultaneously
- **Advanced Proposers**: Genetic algorithms, Bayesian optimization
- **Real-time Monitoring**: Web dashboard for tracking progress
- **Automatic Hyperparameter Tuning**: Optimize training parameters alongside task order
- **Multi-objective Optimization**: Pareto frontier analysis

## Support

For questions or issues:
1. Check the test integration first: `python test_integration.py`
2. Review the logs in `task_order_search.log`
3. Verify VL-T5 training works independently
4. Check that all dependencies are installed
