"""
Test Task Order Override Functionality

This script tests whether the task order override is working correctly
and identifies where the problem might be.
"""

import sys
from pathlib import Path
import numpy as np

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from Question_type import (
    set_custom_task_order, 
    get_task_order, 
    reset_task_order, 
    All_task,
    convert_result_matrix_to_numpy
)


def test_task_order_functions():
    """Test the basic task order functions."""
    
    print("=" * 60)
    print("TESTING TASK ORDER FUNCTIONS")
    print("=" * 60)
    
    # Test 1: Default order
    print("Test 1: Default task order")
    default_order = get_task_order()
    print(f"Default order: {default_order}")
    print(f"All_task: {All_task}")
    assert default_order == All_task, "Default order should match All_task"
    print("✓ Default order test passed")
    
    # Test 2: Set custom order
    print("\nTest 2: Set custom task order")
    custom_order = ['q_color', 'q_recognition', 'q_location', 'q_count']
    set_custom_task_order(custom_order)
    current_order = get_task_order()
    print(f"Set custom order: {custom_order}")
    print(f"Retrieved order: {current_order}")
    assert current_order == custom_order, "Custom order not set correctly"
    print("✓ Custom order test passed")
    
    # Test 3: Reset order
    print("\nTest 3: Reset task order")
    reset_task_order()
    reset_order = get_task_order()
    print(f"Reset order: {reset_order}")
    assert reset_order == All_task, "Reset order should match All_task"
    print("✓ Reset order test passed")
    
    print("\n✓ All task order function tests passed!")


def test_matrix_conversion():
    """Test the matrix conversion function."""
    
    print("\n" + "=" * 60)
    print("TESTING MATRIX CONVERSION")
    print("=" * 60)
    
    # Create a sample result matrix
    task_order = ['q_recognition', 'q_color', 'q_location']
    canonical_tasks = All_task
    
    # Create sample result matrix
    result_matrix = {
        'q_recognition': {
            'q_recognition': 75.0,
            'q_color': 0.0,
            'q_location': 0.0,
            'q_causal': 0.0,
            'q_commonsense': 0.0,
            'q_subcategory': 0.0,
            'q_count': 0.0,
            'q_action': 0.0,
            'q_type': 0.0,
            'q_judge': 0.0
        },
        'q_color': {
            'q_recognition': 70.0,
            'q_color': 80.0,
            'q_location': 0.0,
            'q_causal': 0.0,
            'q_commonsense': 0.0,
            'q_subcategory': 0.0,
            'q_count': 0.0,
            'q_action': 0.0,
            'q_type': 0.0,
            'q_judge': 0.0
        },
        'q_location': {
            'q_recognition': 65.0,
            'q_color': 75.0,
            'q_location': 70.0,
            'q_causal': 0.0,
            'q_commonsense': 0.0,
            'q_subcategory': 0.0,
            'q_count': 0.0,
            'q_action': 0.0,
            'q_type': 0.0,
            'q_judge': 0.0
        }
    }
    
    print(f"Task order: {task_order}")
    print(f"Canonical tasks: {canonical_tasks}")
    
    # Convert to numpy matrix
    performance_matrix = convert_result_matrix_to_numpy(
        result_matrix, task_order, canonical_tasks
    )
    
    print(f"Performance matrix shape: {performance_matrix.shape}")
    print("Performance matrix:")
    print(performance_matrix)
    
    # Verify the conversion
    expected_shape = (len(task_order), len(canonical_tasks))
    assert performance_matrix.shape == expected_shape, f"Wrong shape: {performance_matrix.shape} vs {expected_shape}"
    
    # Check specific values
    assert performance_matrix[0, 0] == 75.0, "Wrong value at [0,0]"  # q_recognition after training q_recognition
    assert performance_matrix[1, 0] == 70.0, "Wrong value at [1,0]"  # q_recognition after training q_color
    assert performance_matrix[1, 2] == 80.0, "Wrong value at [1,2]"  # q_color after training q_color
    
    print("✓ Matrix conversion test passed!")


def analyze_saved_matrix():
    """Analyze the saved matrix to understand the discrepancy."""
    
    print("\n" + "=" * 60)
    print("ANALYZING SAVED MATRIX")
    print("=" * 60)
    
    # Load the saved matrix
    matrix_path = Path("../VL-T5/snap/checkpoint/performance_matrix.npy")
    if not matrix_path.exists():
        matrix_path = Path("VL-T5/snap/checkpoint/performance_matrix.npy")
    
    if matrix_path.exists():
        performance_matrix = np.load(matrix_path)
        print(f"Loaded matrix shape: {performance_matrix.shape}")
        print("Matrix:")
        print(performance_matrix)
        
        # Analyze the pattern
        print("\nAnalyzing matrix pattern:")
        for step in range(performance_matrix.shape[0]):
            non_zero_cols = np.where(performance_matrix[step, :] > 0)[0]
            print(f"Step {step}: Non-zero columns: {non_zero_cols}")
            
            # Check if this matches the expected pattern
            if step < len(All_task):
                expected_task = All_task[step]
                expected_col = step  # Should be diagonal for default order
                print(f"  Expected task: {expected_task} (column {expected_col})")
                
                if step in non_zero_cols:
                    print(f"  ✓ Task {expected_task} has non-zero value at step {step}")
                else:
                    print(f"  ✗ Task {expected_task} has zero value at step {step}")
        
        # Check if this is the default order pattern
        is_default_pattern = True
        for step in range(min(performance_matrix.shape[0], len(All_task))):
            if performance_matrix[step, step] == 0:
                is_default_pattern = False
                break
        
        if is_default_pattern:
            print("\n⚠️  MATRIX FOLLOWS DEFAULT ORDER PATTERN!")
            print("This suggests the custom task order was NOT used during training.")
        else:
            print("\n✓ Matrix does NOT follow default order pattern.")
    else:
        print("No saved matrix found")


def test_vl_t5_integration():
    """Test if VL-T5 script properly reads the custom task order."""
    
    print("\n" + "=" * 60)
    print("TESTING VL-T5 INTEGRATION")
    print("=" * 60)
    
    # Set a custom order
    test_order = ['q_judge', 'q_color', 'q_recognition', 'q_location']
    print(f"Setting test order: {test_order}")
    set_custom_task_order(test_order)
    
    # Import the VL-T5 module to see if it reads the order correctly
    try:
        sys.path.append(str(Path(__file__).parent.parent / "VL-T5" / "src"))
        
        # This should import and read the custom task order
        from vqacl2 import get_task_order as vl_t5_get_task_order
        
        vl_t5_order = vl_t5_get_task_order()
        print(f"VL-T5 reads order as: {vl_t5_order}")
        
        if vl_t5_order == test_order:
            print("✓ VL-T5 correctly reads custom task order")
        else:
            print("✗ VL-T5 does NOT read custom task order correctly")
            print("This is the source of the problem!")
            
    except ImportError as e:
        print(f"Could not import VL-T5 module: {e}")
        print("Testing with direct import...")
        
        # Test direct import
        current_order = get_task_order()
        print(f"Direct import reads order as: {current_order}")
        
        if current_order == test_order:
            print("✓ Direct import works correctly")
        else:
            print("✗ Direct import also fails")
    
    # Reset for cleanup
    reset_task_order()


def main():
    """Run all tests."""
    
    print("TASK ORDER OVERRIDE TESTING")
    print("=" * 80)
    
    try:
        test_task_order_functions()
        test_matrix_conversion()
        analyze_saved_matrix()
        test_vl_t5_integration()
        
        print("\n" + "=" * 80)
        print("TESTING COMPLETE")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
