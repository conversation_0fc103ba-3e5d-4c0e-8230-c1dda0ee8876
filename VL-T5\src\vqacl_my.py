
import torch.backends.cudnn as cudnn
import torch.multiprocessing as mp
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
import os
import collections
from pathlib import Path
from packaging import version
import numpy as np
from tqdm import tqdm
import torch
import torch.nn as nn
import logging
import shutil
from pprint import pprint
from param import parse_args
import sys
from vqa_data_memory import get_loader, get_loader_test, VQADataset, get_loader_memory
from utils import load_state_dict, LossMeter, set_global_logging_level
import dist_utils
import json
import random
import copy
import torch.optim as optim
import torch.nn.utils.rnn as rnn


proj_dir = Path(__file__).resolve().parent.parent

_use_native_amp = False
_use_apex = False

# Check if Pytorch version >= 1.6 to switch between Native AMP and Apex
if version.parse(torch.__version__) < version.parse("1.6"):
    from transormers.file_utils import is_apex_available
    if is_apex_available():
        from apex import amp
    _use_apex = True
else:
    _use_native_amp = True
    from torch.cuda.amp import autocast

from trainer_base import TrainerBase
#  10 task
from Question_type import All_task, Comp_task, show_results_matrix, evaluate_metric, Category_splits, ImgId_cate_map, random_dic



def save_batches(train_loader_cate, task, cateGroup, save_dir='/mnt/DATA/VQA_Files/'):
    os.makedirs(save_dir, exist_ok=True)
    
    for batch_idx, now_batch in enumerate(train_loader_cate):
        batch = {
            "sent": now_batch["sent"],
            "answers": now_batch["answers"],
            "question_ids": now_batch["question_ids"]
        }
        # Save the full batch as a single .pt file
        save_path = os.path.join(save_dir, f'batch_task{task}_cate{cateGroup}_batch{batch_idx}.pt')
        torch.save(batch, save_path)

def log_to_file(message, filename="debug_log2.txt"):
    """Helper function to log messages to a file."""
    with open(filename, "a") as file:
        file.write(message + "\n")

def cycle(iterable):
    # iterate with shuffling
    while True:
        for i in iterable:
            yield i

class QuestionClassifier(nn.Module):
    def __init__(self, vocab_size, embed_dim, hidden_dim, num_classes):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, embed_dim, padding_idx=0)
        self.lstm = nn.LSTM(embed_dim, hidden_dim, batch_first=True)
        self.fc = nn.Linear(hidden_dim, num_classes)
    def forward(self, input_ids, lengths):
        x = self.embedding(input_ids)

        # Pack padded sequence to ignore padding
        x_packed = rnn.pack_padded_sequence(x, lengths.cpu(), batch_first=True, enforce_sorted=False)
        
        _, (hidden, _) = self.lstm(x_packed)  # Get last hidden state
        logits = self.fc(hidden[-1])  # Fully connected layer
        return logits



class Trainer(TrainerBase):
    def __init__(self, args, coco_Ours, train_loader=None, val_loader=None, test_loader=None, train=True):
        self.result_matrix = {}
        self.task_list = []
        for task in coco_Ours:
            self.result_matrix[task] = {}
            self.task_list.append(task)

        self.train_loader_dict = {}
        self.val_loader_dict = {}
        self.test_loader_dict = {}
        self.test_loader_dict_all = {}

        self.train_dset = VQADataset(args.train, True)
        self.val_dset = VQADataset(args.valid, True)
        self.test_dset = VQADataset(args.test, True)
        super().__init__(
            args,
            train=train)

        if not self.verbose:
            set_global_logging_level(logging.ERROR, ["transformers"])

        from vqa_model import VLT5VQA

        model_kwargs = {}
        if 't5' in args.backbone:
            model_class = VLT5VQA

        config = self.create_config()
        self.tokenizer = self.create_tokenizer()
        if 'bart' in self.args.tokenizer:
            num_added_toks = 0
            if config.use_vis_order_embedding:
                additional_special_tokens = [f'<extra_id_{i}>' for i in range(100-1, -1, -1)] + \
                        [f'<vis_extra_id_{i}>' for i in range(100-1, -1, -1)]
                special_tokens_dict = {'additional_special_tokens': additional_special_tokens}
                num_added_toks = self.tokenizer.add_special_tokens(special_tokens_dict)

                config.default_obj_order_ids = self.tokenizer.convert_tokens_to_ids([f'<vis_extra_id_{i}>' for i in range(100)])

        self.model = self.create_model(model_class, config, **model_kwargs)
        
        if 't5' in self.args.tokenizer:
            self.model.resize_token_embeddings(self.tokenizer.vocab_size)
        elif 'bart' in self.args.tokenizer:
            self.model.resize_token_embeddings(self.model.model.shared.num_embeddings + num_added_toks)

        self.model.tokenizer = self.tokenizer

        # Load Checkpoint
        self.start_epoch = None
        if args.load is not None:
            ckpt_path = args.load + '.pth'
            self.load_checkpoint(ckpt_path)

        # --------------------- random seed --------------------#
        print(f'self.args.from_scratch: {self.args.from_scratch}')
        if self.args.from_scratch:
            if args.ifseed:
                self.init_weights(seed=args.seed, ifseed=True)
            else:
                self.init_weights()

        # GPU Options
        print(f'Model Launching at GPU {self.args.gpu}')
        if self.verbose:
            from time import time
            start = time()

        self.model = self.model.to(args.gpu)
        #INDEPENDENT MODELS FOR EACH TASK
        #self.models = [copy.deepcopy(self.model) for _ in range(len(coco_Ours))]

        if args.multiGPU:
            if args.distributed:
                print('Using DistributedDataParallel')
                self.model = DDP(self.model, device_ids=[args.gpu],
                                 find_unused_parameters=True
                                 )
        if self.verbose:
            print(f'It took {time() - start:.1f}s')
        self.iftrain = train
        self.coco_Ours = coco_Ours

        self.task_iftrain = {}
        for task in self.coco_Ours:
            self.task_iftrain[task] = 0

        self.task_total_num = torch.zeros(len(self.task_list))

        self.M = args.m_size
        self.Examplar_set = {'G1':[], 'G2':[], 'G3':[], 'G4':[], 'G5':[]}
        self.composition_test_cate = args.comp_cate



    def train(self, load=False):
        if load == True:
            latest_task = args.checkpoint
            latest_task_idx = self.task_list.index(latest_task)
            for idx, task in enumerate(self.task_list):
                if idx <=latest_task_idx:
                    self.task_iftrain[task] = 1

            checkpoint_model = self.args.output + '/' + args.checkpoint+'_LAST'
            # if not os.path.exists(self.args.output):
            #     os.mkdir(self.args.output)
            # best_path = os.path.join(self.args.output, task+'_BEST') # BEST or LAST?
            self.load(checkpoint_model)
            print('Success to load the checkpoint from the task ', latest_task)

        else:
            latest_task_idx = -1

        #print(self.task_list) #['q_recognition', 'q_location', 'q_judge', 'q_commonsense', 'q_count', 'q_action', 'q_color', 'q_type', 'q_subcategory', 'q_causal']
        #print('======================== LOADING MODEL "', latest_task_idx+1, '" ========================')
        #self.model = self.models[latest_task_idx+1]

        # # #
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f'Total parameters in the model: {total_params}')
        
        # for param in self.model.parameters():
        #     param.requires_grad = False

        # for name, param in self.model.named_parameters():
        #     if (
        #         "visual_embedding" in name
        #         or "adapter" in name
        #         or "lm_head" in name
        #     ):
        #         param.requires_grad = True


        # trainable_params = 0
        # for name, param in self.model.named_parameters():
        #     if f"block" in name:
        #         param.requires_grad = True
        #         trainable_params += param.numel()
        #         print(f"Trainable parameter: {name}")
        #     # if f"prototype" in name:
        #     #     param.requires_grad = True
        #     #     trainable_params += param.numel()
        #     #     print(f"Trainable parameter: {name}")

        # print(f'Trainable parameters count: {trainable_params}')
        # # #

        # for name, param in self.model.module.named_parameters():
        #     print(f"Parameter name: {name}")
        #     print(f"Parameter requires_grad: {param.requires_grad}")
        #     print(f"Parameter shape: {param.shape}")
        #     print()
        # sys.exit()

        # for param in self.model.module.parameters():
        #     param.requires_grad = False  # Freeze all parameters in the model

        # for param in self.model.module.encoder.block.parameters():  # Unfreeze adapter layers in the encoder
        #     param.requires_grad = True
        # for param in self.model.module.decoder.block.parameters():  # Unfreeze adapter layers in the decoder
        #     param.requires_grad = True


        # trainable_params = 0
        # for name, param in self.model.module.named_parameters():
        #     if f"adapter" in name:
        #         param.requires_grad = True
        #         trainable_params += param.numel()
        #         print(f"Trainable parameter: {name}")






                
        # sys.exit()

        # print(self.model)
        # sys.exit()

        for task_idx, task in enumerate(self.task_list[latest_task_idx+1:]):  # for each task, train for several epochs
            print('======================== Now is task "', task, '" ========================')
            self.task_iftrain[task] = 1

            # Memory
            if args.memory:
                if task_idx != latest_task_idx + 1:
                    each_memory = int(self.M / task_idx)
                    data_info_path = ('/mnt/d/vqacl/datasets/vqa/Partition_Q/karpathy_train_' + f'{self.task_list[task_idx - 1]}.json')
                    with open(data_info_path) as f:
                        data_info_dicts = json.load(f)

                    random.shuffle(data_info_dicts)  # shuffle
                    each_memory_for_cate = int(each_memory / len(Category_splits))

                    for cate in Category_splits:
                        num = 0
                        self.Examplar_set[cate].append([])
                        for _d in data_info_dicts:
                            img_id = _d['img_id']
                            if img_id in ImgId_cate_map:
                                if ImgId_cate_map[img_id] in Category_splits[cate]:
                                    self.Examplar_set[cate][task_idx - 1].append(_d)
                                    num += 1
                                    if num >= each_memory_for_cate:
                                        break

                    print('Load from Partition_Q_v3......')

                    for cate in Category_splits:
                        for i in range(task_idx):
                            self.Examplar_set[cate][i] = self.Examplar_set[cate][i][: each_memory_for_cate]

                    All_examplar = []
                    for E_set in self.Examplar_set:
                        for task_set in self.Examplar_set[E_set]:
                            All_examplar += task_set
                    # assert len(All_examplar) == M
                    print("# The size of the cate Memory:", len(All_examplar))
                else:
                    All_examplar = []
                    each_memory = 0
            else:
                All_examplar = []
                each_memory = 0

            # Load the data
            print("#Loading ", task)

            train_loader, total_num_Q = get_loader(
                args,
                self.coco_Ours,
                [],
                self.train_dset,
                split=args.train, mode='train', batch_size=args.batch_size,
                distributed=args.distributed, gpu=args.gpu,
                workers=args.num_workers,
                topk=args.train_topk,
                task=task,
            )

            #print('train_loader:', train_loader)   #train_loader: {'G1': <torch.utils.data.dataloader.DataLoader object at 0x79cc6128b4f0>, 'G2': <torch.utils.data.dataloader.DataLoader object at 0x79cf607afcd0>, 'G3': <torch.utils.data.dataloader.DataLoader object at 0x79cc61301340>, 'G4': <torch.utils.data.dataloader.DataLoader object at 0x79cc6129cd00>, 'G5': <torch.utils.data.dataloader.DataLoader object at 0x79ccf5a90820>}
            #print("Train Loader Shape:", len(train_loader)) # 5

            self.task_total_num[task_idx] = total_num_Q


            if args.valid_batch_size is not None:
                self.valid_batch_size = args.valid_batch_size
            else:
                self.valid_batch_size = args.batch_size
            print(f'Building val loader at GPU {args.gpu}')
            val_loader, _ = get_loader(
                args,
                self.coco_Ours,
                [],
                self.val_dset,
                split=args.valid, mode='val', batch_size=self.valid_batch_size,
                distributed=args.distributed, gpu=args.gpu,
                workers=4,
                topk=args.valid_topk,
                task=task,
            )

            print(f'Building test loader at GPU {args.gpu}')
            test_loader, _ = get_loader(
                args,
                self.coco_Ours,
                [],
                self.test_dset,
                split=args.test, mode='val', batch_size=self.valid_batch_size,
                distributed=args.distributed, gpu=args.gpu,
                workers=4,
                topk=args.valid_topk,
                task=task,
            )
            self.test_loader_dict[task] = test_loader

            test_loader = get_loader_test(
                args,
                self.coco_Ours,
                [],
                self.test_dset,
                split=args.test, mode='val', batch_size=self.valid_batch_size,
                distributed=args.distributed, gpu=args.gpu,
                workers=4,
                topk=args.valid_topk,
                task=task,
            )
            self.test_loader_dict_all[task] = test_loader

            print("#Loading ", task)
            memory_loader = get_loader_memory(
                args,
                self.coco_Ours,
                All_examplar,
                self.train_dset,
                split=args.train, mode='train', batch_size=args.batch_size,
                distributed=args.distributed, gpu=args.gpu,
                workers=args.num_workers,
                topk=args.train_topk,
            )  #G1-G5

            if self.verbose:
                loss_meter = LossMeter()
                loss_meter_mem = LossMeter()
                # loss_mem_V = LossMeter()
                # loss_mem_Q_new = LossMeter()
                # loss_mem_V_new = LossMeter()
                best_valid = 0.
                best_epoch = 0

                if 't5' in self.args.backbone:
                    if self.args.use_vision:
                        project_name = "VLT5_VQA"
                    else:
                        project_name = "T5_VQA"
                elif 'bart' in self.args.backbone:
                    if self.args.use_vision:
                        project_name = "VLBart_VQA"
                    else:
                        project_name = "Bart_VQA"

                src_dir = Path(__file__).resolve().parent
                base_path = str(src_dir.parent)
                src_dir = str(src_dir)
                # wandb.save(os.path.join(src_dir + "/*.py"), base_path=base_path)

            if self.args.distributed:
                dist.barrier()

            global_step = 0
            #print(Category_splits)
            Category_splits_random = random_dic(Category_splits)
            # Category_splits_random = Category_splits
            # print('Category_splits_random', Category_splits_random) #Question_type.py file a ache
            # sys.exit()
            #{'G1': [58, 48, 55, 36, 64, 1, 70, 73, 42, 15, 6, 18, 49, 59, 31, 2], 'G2': [19, 77, 22, 9, 24, 53, 12, 13, 78, 50, 47, 41, 32, 28, 54, 23], 'G4': [35, 29, 66, 40, 43, 26, 72, 10, 38, 61, 76, 44, 75, 69, 16, 57], 'G5': [45, 33, 63, 56, 21, 11, 62, 74, 17, 52, 46, 30, 27, 51, 37, 7], 'G3': [60, 8, 34, 25, 67, 4, 14, 68, 3, 79, 0, 5, 65, 20, 71, 39]}
            
            #################
            # json_file_path = f"/mnt/d/vqacl/datasets/vqa/Partition_Q/karpathy_train_{task}.json"
            # with open(json_file_path, "r") as file:
            #     json_data = json.load(file)
            # qid_to_imgid = {item["question_id"]: item["img_id"] for item in json_data}
            ################

            for idx, cateGroup in enumerate(Category_splits_random):
                print('-------- Training the cate group ', cateGroup,' of task ', task,'------')

                self.train_loader_cate = train_loader[cateGroup]
                self.val_loader_cate = val_loader[cateGroup]
                self.memory_loader_cate = memory_loader[cateGroup]


                # save_batches(self.train_loader_cate, task, cateGroup)
                # print(f"{task} {cateGroup} Batch saved successfully.")



                # print("Batch keys:", batch.keys()) #Batch keys: dict_keys(['input_ids', 'target_ids', 'boxes', 'vis_feats', 'sent', 'question_ids', 'answers', 'all_answers', 'scores', 'labels', 'args', 'task', 'cate_labels', 'ques_labels'])
                # continue
                # all_input_ids = []
                # all_lengths = []  # List to store lengths of input_ids
                # max_len_all_batches = 20
                # for now_batch in self.train_loader_cate:
                #     batch = now_batch
                #     padded_input_ids = []
                #     lengths = []  # Store the original lengths of input_ids in this batch
                    
                #     for seq in batch['input_ids'].cpu():
                #         lengths.append(seq.shape[0])  # Store the length of each sequence in the batch
                #         padding_size = max_len_all_batches - seq.shape[0]
                #         padded_seq = torch.nn.functional.pad(seq, (0, padding_size), value=0)
                #         padded_input_ids.append(padded_seq)
                    
                #     padded_input_ids = torch.stack(padded_input_ids, dim=0)  # Shape: (batch_size, max_len)
                    
                #     # Add the padded batch to the list
                #     all_input_ids.append(padded_input_ids)
                #     all_lengths.append(lengths)  # Add lengths for this batch

                # # Step 3: Concatenate all batches after padding
                # all_input_ids = torch.cat(all_input_ids, dim=0)
                # all_lengths = [length for sublist in all_lengths for length in sublist]  # Flatten the lengths

                # # Save the padded input_ids and lengths
                # torch.save(all_input_ids, f"{task}_{cateGroup}_input_ids_all.pt")
                # torch.save(all_lengths, f"{task}_{cateGroup}_input_lengths_all.pt")

                # print(f"All input_ids and lengths saved successfully in {task}_{cateGroup}_input_ids_all.pt and {task}_{cateGroup}_input_lengths_all.pt")




                # continue

                # Optimizer
                if self.iftrain:
                    if len(self.memory_loader_cate.dataset) > 0:
                        total_train_num = 2 * len(self.train_loader_cate.dataset)
                    else:
                        total_train_num = len(self.train_loader_cate.dataset)
                    self.optim, self.lr_scheduler = self.create_optimizer_and_scheduler(total_train_num)

                    if self.args.fp16 and _use_native_amp:
                        self.scaler = torch.cuda.amp.GradScaler()
                    elif _use_apex:
                        self.model, self.optim = amp.initialize(
                            self.model, self.optim, opt_level='O1', verbosity=self.verbose)

                if cateGroup == self.composition_test_cate and task != self.task_list[latest_task_idx+1]:
                    print("-------- Pass the training for", cateGroup, 'for after composition testing.--------')
                    continue


                for epoch in range(self.args.epochs):
                    if self.start_epoch is not None:
                        epoch += self.start_epoch
                    self.model.train()

                    if self.args.distributed:
                        self.train_loader_cate.sampler.set_epoch(epoch)
                    if self.verbose:
                        pbar = tqdm(total=len(self.train_loader_cate), ncols=120)

                    epoch_results = {
                        'loss': 0.,
                    }

                    quesid2ans = {}

                    if len(self.memory_loader_cate.dataset) > 0:
                        now_loader = zip(self.train_loader_cate, cycle(self.memory_loader_cate))
                        print('Use memory loader')
                    else:
                        now_loader = self.train_loader_cate

                    for now_batch in now_loader:
                        if len(now_batch) == 2:
                            batch, mem_batch = now_batch
                        else:
                            batch = now_batch
                            mem_batch = None

                        results, lr = self.train_step(batch, epoch_results, task_idx, each_memory)
                        if mem_batch:
                            results_mem, lr = self.train_step(mem_batch, epoch_results, task_idx, each_memory)

                        if self.verbose:
                            loss_meter.update(results['loss'].item())
                            desc_str = f'Epoch {epoch} | LR {lr:.6f}'
                            desc_str += f' | Loss {loss_meter.val:4f}'
                            if mem_batch:
                                loss_meter_mem.update(results_mem['loss'].item())
                                desc_str += f' | Loss_mem {loss_meter_mem.val:4f}'
                            else:
                                loss_meter_mem.update(-1)


                            pbar.set_description(desc_str)
                            pbar.update(1)

                        if self.args.distributed:
                            dist.barrier()
                        # break

                    if self.verbose:
                        pbar.close()

                    print("Loss:",loss_meter.val,' Loss_mem:', loss_meter_mem.val)

                    # Validation
                    score_dict = self.evaluate(self.val_loader_cate)

                    if self.verbose:
                        valid_score = score_dict['topk_score'] * 100.
                        valid_score_raw = score_dict['overall']

                        log_str = ''
                        log_str += "\nGroup %s Epoch %d: Valid Raw %0.2f Topk %0.2f" % (cateGroup, epoch, valid_score_raw, valid_score)

                        print(log_str)

                    if self.args.distributed:
                        dist.barrier()

            # continue

            if self.verbose:
                self.save(task + "_LAST")
            sys.exit()
            # ========= Testing =========
            self.test(task)
        # sys.exit()
        try:
            Q_prototype = self.model.module.Q_prototype
            V_prototype = self.model.module.V_prototype
            torch.save(Q_prototype, args.output + "/Q_prototype.pt")
            torch.save(V_prototype, args.output + "/V_prototype.pt")
            print(" ======= Saved the learned prototypes ======= ")
        except:
            print('save prototype error')


    def train_step(self, batch, epoch_results, task_idx, each_memory):
        if self.args.fp16 and _use_native_amp:
            with autocast():
                if self.args.distributed:
                    results = self.model.module.train_step(batch)
                else:
                    results = self.model.train_step(batch)
        else:  # this
            if self.args.distributed:
                results = self.model.module.train_step(batch, task_idx, self.args.proto_alpha, self.args.proto_beta, each_memory, self.task_total_num)
            else:
                results = self.model.train_step(batch)

        loss = results['loss']
        lambda_Q = self.args.lambda_Q
        lambda_V = self.args.lambda_V
        lambda_Q_new = self.args.lambda_Q_new
        lambda_V_new = self.args.lambda_V_new

        #print("HERE")
        if 'loss_memory' in results:
            (loss_memory_Q, loss_memory_V) = results['loss_memory']
            loss = loss + lambda_Q * loss_memory_Q + lambda_V * loss_memory_V
        if 'loss_memory_new' in results:
            (loss_memory_Q_new, loss_memory_V_new) = results['loss_memory_new']
            loss = loss + lambda_Q_new * loss_memory_Q_new + lambda_V_new * loss_memory_V_new

        if self.args.fp16 and _use_native_amp:
            self.scaler.scale(loss).backward()
        elif self.args.fp16 and _use_apex:
            with amp.scale_loss(loss, self.optim) as scaled_loss:
                scaled_loss.backward()
        else:  # this
            #print(f"HERE Print {loss.requires_grad}, {loss.grad_fn}")
            loss.backward()

        loss = loss.detach()

        # Update Parameters
        if self.args.clip_grad_norm > 0:
            if self.args.fp16 and _use_native_amp:
                self.scaler.unscale_(self.optim)
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), self.args.clip_grad_norm)
            elif self.args.fp16 and _use_apex:
                torch.nn.utils.clip_grad_norm_(amp.master_params(
                    self.optim), self.args.clip_grad_norm)
            else:  # this
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), self.args.clip_grad_norm)

        if self.args.fp16 and _use_native_amp:
            self.scaler.step(self.optim)
            self.scaler.update()
        else:  # this
            self.optim.step()

        if self.lr_scheduler:
            self.lr_scheduler.step()
        for param in self.model.parameters():
            param.grad = None

        # ----------
        # self.model.module.Q_prototype_param.data.clamp_(0, 1)
        # self.model.module.V_prototype_param.data.clamp_(0, 1)

        # global_step += 1

        for k, v in results.items():
            if k in epoch_results:
                epoch_results[k] += v.item()

        if self.lr_scheduler:
            if version.parse(torch.__version__) >= version.parse("1.4"):
                lr = self.lr_scheduler.get_last_lr()[0]
            else:
                lr = self.lr_scheduler.get_lr()[0]
        else:
            try:
                lr = self.optim.get_lr()[0]
            except AttributeError:
                lr = self.args.lr
        return results, lr

    def Test(self, load=False):
        for task_idx, task in enumerate(self.task_list):
            print('======================== Now is task "', task, '" ========================')

            test_loader = get_loader_test(
                args,
                self.coco_Ours,
                [],
                self.test_dset,
                split=args.test, mode='val', batch_size=args.valid_batch_size,
                distributed=args.distributed, gpu=args.gpu,
                workers=4,
                topk=args.valid_topk,
                task=task,
            )
            self.test_loader_dict_all[task] = test_loader

        # ========= Testing =========
        self.test(self.task_list[-1])


    def test(self, task, comp=False):
        # Test Set
        if not os.path.exists(self.args.output):
            os.mkdir(self.args.output)
        last_path = os.path.join(self.args.output, task + '_LAST')
        #print('Load the last model from ', last_path) #Model loaded from  snap/VQAv2_Our/q_causal_LAST
        
        # self.load(last_path)
        mmm = "q_location" ## original ['q_recognition', 'q_location', 'q_judge', 'q_commonsense', 'q_count', 'q_action', 'q_color', 'q_type', 'q_subcategory', 'q_causal']
        self.load(f"/mnt/d/vqacl/VL-T5/snap/checkpoint/independent/{mmm}_LAST")
        print('Load the last model from ', mmm) #Model loaded from  snap/VQAv2_Our/q_causal_LAST

        #LOAD THE PROTOTYPES FROM DISK
        # if not self.args.now_train:
        #     self.model.module.Q_prototype = torch.load(self.args.output+'/Q_prototype.pt')
        #     self.model.module.V_prototype = torch.load(self.args.output+'/V_prototype.pt')
        #     print('Load the prototypes from ', self.args.output+'/Q_prototype.pt')

        #print(self.task_iftrain) {'q_recognition': 0, 'q_location': 0, 'q_judge': 0, 'q_commonsense': 0, 'q_count': 0, 'q_action': 0, 'q_color': 0, 'q_type': 0, 'q_subcategory': 0, 'q_causal': 0}

        # =========== test for all previous tasks
        flag = 1
        for test_task in self.coco_Ours:
            if self.args.now_train:
                if self.task_iftrain[test_task] == 0: #self.task_iftrain[test_task] == 1 means the task has been trained, shob zero test korar time upore print kora
                    flag = 0
            if flag == 1:
                self.test_loader = self.test_loader_dict_all[test_task] #shob test loader
                print(' ===== Test for the task "' + test_task + '"  ======')
                log_to_file(f" ===== Test for the task {test_task}  ======", filename="my2.txt")


                # all_input_ids = []
                # all_lengths = []  # List to store lengths of input_ids
                # max_len_all_batches = 20
                # for now_batch in self.test_loader:
                #     batch = now_batch
                #     padded_input_ids = []
                #     lengths = []  # Store the original lengths of input_ids in this batch
                    
                #     for seq in batch['input_ids'].cpu():
                #         lengths.append(seq.shape[0])  # Store the length of each sequence in the batch
                #         padding_size = max_len_all_batches - seq.shape[0]
                #         padded_seq = torch.nn.functional.pad(seq, (0, padding_size), value=0)
                #         padded_input_ids.append(padded_seq)
                    
                #     padded_input_ids = torch.stack(padded_input_ids, dim=0)  # Shape: (batch_size, max_len)
                    
                #     # Add the padded batch to the list
                #     all_input_ids.append(padded_input_ids)
                #     all_lengths.append(lengths)  # Add lengths for this batch

                # # Step 3: Concatenate all batches after padding
                # all_input_ids = torch.cat(all_input_ids, dim=0)
                # all_lengths = [length for sublist in all_lengths for length in sublist]  # Flatten the lengths

                # # Save the padded input_ids and lengths
                # torch.save(all_input_ids, f"test_{test_task}_input_ids_all.pt")
                # torch.save(all_lengths, f"test_{test_task}_input_lengths_all.pt")

                # print(f"All input_ids and lengths saved successfully in {test_task}_input_ids_all.pt and {test_task}_input_lengths_all.pt")

                # continue

                quesid2ans = self.predict(self.test_loader)

                if self.verbose:
                    evaluator = self.test_loader.evaluator
                    score_dict = evaluator.evaluate(quesid2ans)

                    acc_dict_all = evaluator.evaluate_raw(quesid2ans)
                    #print('acc_dict_all', acc_dict_all)
                    acc_dict_answerable = evaluator.evaluate_raw(quesid2ans, is_topk_optimal=True)
                    acc_dict_unanswerable = evaluator.evaluate_raw(quesid2ans, is_topk_optimal=False)

                    wandb_log_dict = {}
                    wandb_log_dict['Test/overall'] = acc_dict_all['overall']
                    wandb_log_dict['Test/topk_optimal'] = acc_dict_answerable['overall']
                    wandb_log_dict['Test/topk_not_optimal'] = acc_dict_unanswerable['overall']

                    for qtype, score in acc_dict_all['perQuestionType'].items():
                        wandb_log_dict[f'Test_Qtypes/{qtype}'] = score
                    for atype, score in acc_dict_all['perAnswerType'].items():
                        if atype == 'yes/no':
                            atype = 'yes_no'
                        wandb_log_dict[f'Test_Atypes/{atype}'] = score

                    print(test_task, wandb_log_dict)
                    log_to_file(f'{test_task} {wandb_log_dict}', filename="my2.txt")

                self.result_matrix[task][test_task] = acc_dict_all['overall']

                if self.args.distributed:
                    dist.barrier()


    def predict1(self, loader, dump_path=None):
        print("Starting prediction...")

        self.model.eval()  # Set model to evaluation mode
        with torch.no_grad():  # Disable gradient calculations for inference
            quesid2ans = {}

            if self.verbose:
                print(f"Total batches in loader: {len(loader)}")
                pbar = tqdm(total=len(loader), ncols=120, desc="Prediction---")

            # Iterate through batches
            for i, batch in enumerate(loader):

                print(f"Processing batch {i+1}/{len(loader)}...")

                # Perform inference
                if self.args.distributed:
                    results = self.model.module.test_step(batch)
                else:
                    results = self.model.test_step(batch)

                pred_ans = results['pred_ans']  # Extract generated answers
                ques_ids = batch['question_ids']  # Extract corresponding question IDs

                print(f"Batch {i+1}: Number of predictions = {len(pred_ans)}")

                # Store predictions in dictionary
                for qid, ans in zip(ques_ids, pred_ans):
                    quesid2ans[qid] = ans
                    #print(f"Question ID: {qid}, Predicted Answer: {ans}")
                    #log_to_file(f"Question ID: {qid}, Predicted Answer: {ans}", filename="predictions_log.txt")

                if self.verbose:
                    pbar.update(1)

            if self.verbose:
                pbar.close()

        # Synchronize across distributed GPUs if necessary
        if self.args.distributed:
            print("Synchronizing results across distributed GPUs...")
            dist.barrier()

        qid2ans_list = dist_utils.all_gather(quesid2ans)

        if self.verbose:
            print("Gathering predictions from all processes...")
            quesid2ans = {}
            for qid2ans in qid2ans_list:
                for k, v in qid2ans.items():
                    quesid2ans[k] = v

            # Dump results if needed
            if dump_path is not None:
                print(f"Dumping predictions to {dump_path}...")
                evaluator = loader.evaluator
                evaluator.dump_result(quesid2ans, dump_path)

        print("Prediction complete.")
        
        return quesid2ans
    
    def predict_tid(self, loader, dump_path=None):

        # TASK ID Detection
        tid_model = QuestionClassifier(32128, 256, 128, 10).cuda()
        tid_model.load_state_dict(torch.load("/home/<USER>/mf0463/Downloads/question_classifier.pt", map_location="cuda:0"))
        tid_model.eval()

        # models = []
        tlist = ['q_recognition', 'q_location', 'q_judge', 'q_commonsense', 'q_count', 'q_action', 'q_color', 'q_type', 'q_subcategory', 'q_causal']
        # for ii, tt in enumerate(tlist):
        #     self.load(f"/mnt/d/vqacl/VL-T5/snap/checkpoint/{tt}_LAST")
        #     models.append(copy.deepcopy(self.model))
        #     models[ii].eval()
        
        #self.load(f"/mnt/d/vqacl/VL-T5/snap/checkpoint/q_causal_LAST")
        #self.model.eval()

        
        
        with torch.no_grad():

            quesid2ans = {}

            if self.verbose:
                pbar = tqdm(total=len(loader), ncols=120, desc="Prediction---")

            for i, batch in enumerate(loader):
                batch_size = batch['input_ids'].shape[0]
                lengths = torch.full((batch_size,), 20, dtype=torch.long)

                # Predict task IDs for entire batch
                tid_outputs = tid_model(batch['input_ids'].cuda(), lengths.cuda())
                predictions = torch.argmax(tid_outputs, dim=1)  # shape: (batch_size,)

                # Group by task ID
                task_batches = {}
                for idx, task_id in enumerate(predictions.tolist()):
                    if task_id not in task_batches:
                        task_batches[task_id] = {'indices': [], 'sub_batch': {}}
                    task_batches[task_id]['indices'].append(idx)
                    
                    valid_keys = ['vis_feats','input_ids','boxes','cate_labels','ques_labels']
                    for key in valid_keys:
                        if key in batch:
                            if key not in task_batches[task_id]['sub_batch']:
                                task_batches[task_id]['sub_batch'][key] = []
                            task_batches[task_id]['sub_batch'][key].append(batch[key][idx])

                # Placeholder for ordered results
                outputs_in_order = [None] * batch_size

                # Inference by task group
                for task_id, info in task_batches.items():
                    indices = info['indices']
                    sub_batch = {
                        k: torch.stack(v).cuda() if isinstance(v[0], torch.Tensor) else v
                        for k, v in info['sub_batch'].items()
                    }

                    # self.model = models[task_id]
                    self.load(f"/mnt/d/vqacl/VL-T5/snap/checkpoint/{tlist[task_id]}_LAST")
                    self.model.eval()

                    if self.args.distributed:
                        results = self.model.module.test_step(sub_batch)
                    else:
                        results = self.model.test_step(sub_batch)

                    preds = results['pred_ans']
                    for i, idx in enumerate(indices):
                        outputs_in_order[idx] = preds[i]

                pred_ans = outputs_in_order
                #pred_ans = results['pred_ans'] # generated_sents
                ques_ids = batch['question_ids']

                qu = batch['sent'] ###MEEE
                an = batch['all_answers'] #MEEE

                #for qid, ans in zip(ques_ids, pred_ans):
                for qid, ans, qs, ass in zip(ques_ids, pred_ans, qu, an): #MEEE
                    quesid2ans[qid] = ans
                    # log_to_file(f"Question ID: {qid}, Question: {qs}, Answers: {ass} Predicted Answer: {ans}", filename="predictions_log2.txt")

                if self.verbose:
                    pbar.update(1)

            if self.verbose:
                pbar.close()

        if self.args.distributed:
            dist.barrier()

        qid2ans_list = dist_utils.all_gather(quesid2ans)
        if self.verbose:
            quesid2ans = {}
            for qid2ans in qid2ans_list:
                for k, v in qid2ans.items():
                    quesid2ans[k] = v

            if dump_path is not None:
                evaluator = loader.evaluator
                evaluator.dump_result(quesid2ans, dump_path)


        return quesid2ans

    def predict(self, loader, dump_path=None):

        self.model.eval()

        # data_info_path2 = "/mnt/d/vqacl/VL-T5/src/d_location_q_recognition/generated_questions_q_location_G1.json"
        # data_info_path3 = "/mnt/d/vqacl/VL-T5/src/d_location_q_recognition/generated_questions_q_location_G2.json"
        # data_info_path4 = "/mnt/d/vqacl/VL-T5/src/d_location_q_recognition/generated_questions_q_location_G3.json"
        # data_info_path5 = "/mnt/d/vqacl/VL-T5/src/d_location_q_recognition/generated_questions_q_location_G4.json"
        # data_info_path6 = "/mnt/d/vqacl/VL-T5/src/d_location_q_recognition/generated_questions_q_location_G5.json"

        # # Load additional sets once outside the main file open
        # with open(data_info_path2) as f2:
        #     Examplar_set1 = json.load(f2)
        # with open(data_info_path3) as f3:
        #     Examplar_set2 = json.load(f3)
        # with open(data_info_path4) as f4:
        #     Examplar_set3 = json.load(f4)
        # with open(data_info_path5) as f5:
        #     Examplar_set4 = json.load(f5)
        # with open(data_info_path6) as f6:
        #     Examplar_set5 = json.load(f6)

        with torch.no_grad():
            quesid2ans = {}
            if self.verbose:
                pbar = tqdm(total=len(loader), ncols=120, desc="Prediction---")
            for i, batch in enumerate(loader):   #batch keys: target_ids,boxes,vis_feats,sent,question_ids,answers,all_answers,scores,labels,args,task,cate_labels,ques_labels

                if self.args.distributed:
                    results = self.model.module.test_step(batch)
                else:
                    results = self.model.test_step(batch)

                pred_ans = results['pred_ans'] # generated_sents
                ques_ids = batch['question_ids']

                qu = batch['sent'] ###MEEE
                an = batch['all_answers'] #MEEE

                #for qid, ans in zip(ques_ids, pred_ans):
                for qid, ans, qs, ass in zip(ques_ids, pred_ans, qu, an): #MEEE
                    quesid2ans[qid] = ans
                    log_to_file(f"Question ID: {qid}, Question: {qs}, Answers: {ass} Predicted Answer: {ans}", filename="my2.txt")

                if self.verbose:
                    pbar.update(1)

            if self.verbose:
                pbar.close()

        if self.args.distributed:
            dist.barrier()

        qid2ans_list = dist_utils.all_gather(quesid2ans)
        if self.verbose:
            quesid2ans = {}
            for qid2ans in qid2ans_list:
                for k, v in qid2ans.items():
                    quesid2ans[k] = v

            if dump_path is not None:
                evaluator = loader.evaluator
                evaluator.dump_result(quesid2ans, dump_path)


        # jjjj=1
        # for data in [Examplar_set1,Examplar_set2,Examplar_set3,Examplar_set4,Examplar_set5]:
        #     for item in data:
        #         qid = item['question_id']
        #         if qid in quesid2ans:
        #             predicted_answer = quesid2ans[qid]
        #             # Update main 'label' field
        #             item['label'] = {predicted_answer: 1}
        #             # Update all 'answers'
        #             for ans in item['answers']:
        #                 ans['answer'] = predicted_answer

        #     # Step 4: Save the updated JSON
        #     with open(f'/mnt/d/vqacl/VL-T5/src/d_location_q_recognition/Ugenerated_questions_q_location_G{jjjj}.json', 'w') as f:
        #         json.dump(data, f, indent=4)
        #     print(f"Updated JSON saved as /mnt/d/vqacl/VL-T5/src/d_location_q_recognition/Ugenerated_questions_q_location_G{jjjj}.json")
        #     jjjj+=1


        return quesid2ans

    def evaluate(self, loader, dump_path=None):
        quesid2ans = self.predict(loader, dump_path)

        if self.verbose:
            evaluator = loader.evaluator
            acc_dict = evaluator.evaluate_raw(quesid2ans)
            topk_score = evaluator.evaluate(quesid2ans)
            acc_dict['topk_score'] = topk_score

            return acc_dict
import sys
def main_worker(gpu, args):
    gpu=0 #MEEEEEEE
    # GPU is assigned
    args.gpu = gpu
    args.rank = gpu
    print(f'Process Launching at GPU {gpu}')

    if args.distributed:
        torch.cuda.set_device(args.gpu)
        dist.init_process_group(backend='nccl')

    print(f'Building train loader at GPU {gpu}')

    coco_Ours = All_task # original ['q_recognition', 'q_location', 'q_judge', 'q_commonsense', 'q_count', 'q_action', 'q_color', 'q_type', 'q_subcategory', 'q_causal']
    # reverse ['q_causal', 'q_subcategory', 'q_type', 'q_color', 'q_action', 'q_count', 'q_commonsense', 'q_judge', 'q_location', 'q_recognition']
    # [ "q_commonsense", "q_causal", "q_action", "q_recognition", "q_type", "q_subcategory", "q_color", "q_location", "q_judge", "q_count" ]
    # ['q_location', 'q_recognition', 'q_count', 'q_color', 'q_type', 'q_subcategory', 'q_judge', 'q_commonsense', 'q_action', 'q_causal']
    #coco_Ours = ['q_location', 'q_recognition', 'q_count', 'q_color', 'q_type', 'q_subcategory', 'q_judge', 'q_commonsense', 'q_action', 'q_causal']
    # coco_Ours = ['q_location', 'q_recognition', 'q_count', 'q_color', 'q_type', 'q_subcategory', 'q_judge', 'q_commonsense', 'q_action', 'q_causal']
    # coco_Ours = ['q_location']
    #CHATGPT SUGGESTED ORDER coco_Ours = ["q_subcategory", "q_commonsense", "q_color", "q_count", "q_recognition", "q_type", "q_location", "q_action", "q_judge", "q_causal"]
    trainer = Trainer(args, coco_Ours, train=True)

    if args.now_train:
        if args.checkpoint != 'None':
            trainer.train(load=True)
        else:
            trainer.train(load=False)

        print('#------------------ result_matrix --------------------#')
        show_results_matrix(trainer.result_matrix)
        path = args.output + 'results_matrix.json'
        # save_results_matrix(trainer.result_matrix, path)
        metric_dict = evaluate_metric(trainer.result_matrix)
        print('#------  Metric  ------#')
        print('Incremental avg accuracy:', metric_dict['Incre_avg_acc'])
        print('*** Avg accuracy ***', metric_dict['Avg_acc'])
        print('Incremental avg forget:', metric_dict['Incre_avg_forget'])
        print('*** Avg forget ***', metric_dict['Avg_forget'])
        print('6Q Incremental avg accuracy:', metric_dict['Incre_avg_acc_6Q'])
        print('*** _6Q Avg accuracy ***', metric_dict['Avg_acc_6Q'])
        print('_6Q Incremental avg forget:', metric_dict['Incre_avg_forget_6Q'])
        print('*** _6Q Avg forget ***', metric_dict['Avg_forget_6Q'])

    else:
        if args.checkpoint!='None':
            trainer.Test(load=True)
        else:
            trainer.Test(load=False)

        try:
            print('#------------------ Final Performance --------------------#')
            log_to_file(f'#------------------ Final Performance --------------------#')
            print(trainer.result_matrix['q_causal'])
            log_to_file(str(trainer.result_matrix['q_causal']))
            acc = 0
            for key in trainer.result_matrix['q_causal']:
                acc += trainer.result_matrix['q_causal'][key]
            print('AP:', round(acc/10, 4))
            log_to_file(f'AP: {round(acc/10, 4)}')

        except:
            pass

if __name__ == "__main__":
    cudnn.benchmark = True
    args = parse_args()
    ngpus_per_node = 1
    args.world_size = ngpus_per_node
    if args.local_rank in [0, -1]:
        print(args)

        comments = []
        if args.load is not None:
            ckpt_str = "_".join(args.load.split('/')[-3:])
            comments.append(ckpt_str)
            print("IF load the checkpoint from the task ", ckpt_str)

        else:
            ckpt_str = 'scrach'
            comments.append(ckpt_str)
            print("Else load the checkpoint from the task ", ckpt_str)
        if args.comment != '':
            comments.append(args.comment)
        comment = '_'.join(comments)
        print("comment ", comment)
        from datetime import datetime
        current_time = datetime.now().strftime('%b%d_%H-%M')

        run_name = f'{current_time}_GPU{args.world_size}'
        if len(comments) > 0:
            run_name += f'_{comment}'

        args.run_name = run_name
        print("args.run_name  ", args.run_name )

    if args.distributed:
        main_worker(args.local_rank, args)