"""
Debug Task Order Usage

This script creates a simple test to verify that the custom task order
is actually being used during training.
"""

import sys
import subprocess
import time
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from Question_type import set_custom_task_order, get_task_order, reset_task_order, All_task


def test_simple_custom_order():
    """Test with a very simple custom order to see if it's used."""
    
    print("=" * 80)
    print("TESTING SIMPLE CUSTOM TASK ORDER")
    print("=" * 80)
    
    # Set a very obvious custom order (reverse of default)
    custom_order = list(reversed(All_task))
    print(f"Default order: {All_task}")
    print(f"Custom order:  {custom_order}")
    
    # Set the custom order
    set_custom_task_order(custom_order)
    
    # Verify it's set
    current_order = get_task_order()
    print(f"Retrieved order: {current_order}")
    
    if current_order == custom_order:
        print("✓ Custom order set correctly")
    else:
        print("✗ Custom order NOT set correctly")
        return False
    
    print("\nNow running a minimal VL-T5 training to see what order is actually used...")
    
    # Run VL-T5 training with minimal parameters
    try:
        # Change to VL-T5 directory
        vl_t5_dir = Path("../VL-T5") if Path("../VL-T5").exists() else Path("VL-T5")
        
        # Create a minimal training command that will just print the task order
        cmd = [
            "python", "src/vqacl2.py",
            "--train", "datasets/vqa/karpathy_train",
            "--valid", "datasets/vqa/karpathy_val", 
            "--test", "datasets/vqa/karpathy_test",
            "--batch_size", "1",
            "--epochs", "1",
            "--output", "snap/debug_test",
            "--backbone", "t5-base",
            "--now_train",
            "--from_scratch"
        ]
        
        print(f"Running command in {vl_t5_dir}: {' '.join(cmd)}")
        
        # Execute the command and capture output
        result = subprocess.run(
            cmd,
            cwd=vl_t5_dir,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        print("STDOUT:")
        print(result.stdout)
        print("\nSTDERR:")
        print(result.stderr)
        
        # Look for the task order in the output
        if "Using task order:" in result.stdout:
            lines = result.stdout.split('\n')
            for line in lines:
                if "Using task order:" in line:
                    print(f"\n🔍 FOUND TASK ORDER IN OUTPUT: {line}")
                    
                    # Check if it matches our custom order
                    if str(custom_order) in line:
                        print("✅ SUCCESS: Custom task order is being used!")
                        return True
                    elif str(All_task) in line:
                        print("❌ PROBLEM: Default task order is being used instead!")
                        return False
                    else:
                        print("⚠️  UNCLEAR: Task order format is different than expected")
                        return False
        else:
            print("❌ Could not find task order in output")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Training timed out")
        return False
    except Exception as e:
        print(f"❌ Error running training: {e}")
        return False
    
    finally:
        # Always reset the task order
        reset_task_order()


def test_task_order_persistence():
    """Test if the task order persists across different imports."""
    
    print("\n" + "=" * 80)
    print("TESTING TASK ORDER PERSISTENCE")
    print("=" * 80)
    
    # Set custom order
    test_order = ['q_judge', 'q_recognition', 'q_color']
    set_custom_task_order(test_order)
    
    # Test direct import
    from Question_type import get_task_order as direct_get_order
    direct_order = direct_get_order()
    print(f"Direct import order: {direct_order}")
    
    # Test import from VL-T5 context
    try:
        sys.path.append(str(Path(__file__).parent.parent / "VL-T5" / "src"))
        
        # This simulates what happens when VL-T5 imports Question_type
        import importlib
        import Question_type
        importlib.reload(Question_type)
        
        vl_t5_order = Question_type.get_task_order()
        print(f"VL-T5 context order: {vl_t5_order}")
        
        if vl_t5_order == test_order:
            print("✅ Task order persists in VL-T5 context")
            return True
        else:
            print("❌ Task order does NOT persist in VL-T5 context")
            return False
            
    except Exception as e:
        print(f"Error testing VL-T5 context: {e}")
        return False
    
    finally:
        reset_task_order()


def main():
    """Run all debug tests."""
    
    print("DEBUGGING TASK ORDER USAGE")
    print("=" * 80)
    
    # Test 1: Simple custom order
    success1 = test_simple_custom_order()
    
    # Test 2: Task order persistence
    success2 = test_task_order_persistence()
    
    print("\n" + "=" * 80)
    print("DEBUG RESULTS")
    print("=" * 80)
    
    if success1:
        print("✅ Custom task order is being used in VL-T5 training")
    else:
        print("❌ Custom task order is NOT being used in VL-T5 training")
    
    if success2:
        print("✅ Task order persists across imports")
    else:
        print("❌ Task order does NOT persist across imports")
    
    if success1 and success2:
        print("\n🎉 Task order system is working correctly!")
        print("The issue must be elsewhere (e.g., matrix conversion, result saving)")
    else:
        print("\n🔧 Task order system needs fixing")
        print("Recommendations:")
        if not success1:
            print("- Check if VL-T5 is actually using the custom task order")
            print("- Verify the task order is not being reset during training")
        if not success2:
            print("- Check if imports are causing the task order to reset")
            print("- Consider using environment variables or config files")


if __name__ == "__main__":
    main()
