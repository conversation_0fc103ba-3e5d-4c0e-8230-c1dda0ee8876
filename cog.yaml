build:
  gpu: true
  cuda: "11.1"
  python_version: "3.8"
  system_packages:
    - "libgl1-mesa-glx"
    - "libglib2.0-0"
  python_packages:
    - "torch==1.10.0"
    - "transformers==4.2.1"
    - "sentencepiece==0.1.96"
    - "h5py==3.6.0"
    - "wandb==0.12.11"
    - "numpy==1.21.1"
    - "tqdm==4.63.0"
    - "ipython==7.30.1"
    - "pandas==1.4.1"
    - "matplotlib==3.5.1"
    - "pyyaml==6.0"
    - "sacrebleu==2.0.0"
    - "git+https://github.com/j-min/language-evaluation.git"
    - "torchvision==0.11.1"
    - "wget==3.2"
    - "opencv-python==********"
  run:
    - "pip uninstall param -y"

predict: "predict.py:Predictor"
