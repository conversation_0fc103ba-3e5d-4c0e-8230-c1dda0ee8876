"""
Extract Real Metrics from Available Data

This script extracts the actual metrics that we can recover from the existing
results JSON file, with ABSOLUTELY NO fake data generation.

CRITICAL: This script ONLY uses real experimental data.
NO fake/simulated data is generated under any circumstances.
"""

import json
import numpy as np
import sys
from pathlib import Path
from typing import Dict, List, Any

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from Question_type import evaluate_metric, show_results_matrix


def load_results_json(filename: str = "task_order_search_results_20250601_060319.json") -> Dict[str, Any]:
    """Load the results JSON file."""
    with open(filename, 'r') as f:
        return json.load(f)


def load_saved_matrix_and_results():
    """Load the saved performance matrix and results matrix from VL-T5."""

    # Load the numpy performance matrix
    matrix_path = Path("../VL-T5/snap/checkpoint/performance_matrix.npy")
    if not matrix_path.exists():
        matrix_path = Path("VL-T5/snap/checkpoint/performance_matrix.npy")

    if matrix_path.exists():
        performance_matrix = np.load(matrix_path)
        print(f"Loaded performance matrix from: {matrix_path}")
        print(f"Matrix shape: {performance_matrix.shape}")
    else:
        print("No performance matrix found")
        return None, None

    # Load the JSON results matrix
    json_path = Path("../VL-T5/snap/checkpoint/results_matrix.json")
    if not json_path.exists():
        json_path = Path("VL-T5/snap/checkpoint/results_matrix.json")

    if json_path.exists():
        with open(json_path, 'r') as f:
            results_matrix = json.load(f)
        print(f"Loaded results matrix from: {json_path}")
        print(f"Results matrix keys: {list(results_matrix.keys())}")
    else:
        print("No results matrix found")
        return performance_matrix, None

    return performance_matrix, results_matrix


def analyze_available_data():
    """Analyze what data we actually have available."""

    print("=" * 80)
    print("ANALYZING AVAILABLE REAL DATA")
    print("=" * 80)

    # Load the search results
    try:
        search_results = load_results_json()
        print(f"✓ Found search results with {len(search_results['all_results'])} experiments")
    except FileNotFoundError:
        print("✗ No search results JSON found")
        return

    # Load the saved matrices
    performance_matrix, results_matrix = load_saved_matrix_and_results()

    if performance_matrix is not None:
        print(f"✓ Found performance matrix: {performance_matrix.shape}")
    else:
        print("✗ No performance matrix found")

    if results_matrix is not None:
        print(f"✓ Found results matrix with {len(results_matrix)} tasks")
    else:
        print("✗ No results matrix found")

    return search_results, performance_matrix, results_matrix


def display_best_experiment_details(search_results: Dict[str, Any],
                                   performance_matrix: np.ndarray,
                                   results_matrix: Dict[str, Dict[str, float]]):
    """Display the detailed metrics for the best experiment (the only one we have real data for)."""

    print("\n" + "=" * 80)
    print("BEST EXPERIMENT DETAILED ANALYSIS")
    print("=" * 80)

    best_result = search_results['best_result']

    print(f"Task Order: {' → '.join(best_result['task_order'])}")
    print(f"Objective Score: {best_result['objective_score']:.4f}")

    print("\n#------------------ result_matrix --------------------#")
    if results_matrix:
        show_results_matrix(results_matrix)
    else:
        print("Results matrix not available")

    if results_matrix:
        print("\n#------  Metric  ------#")
        # Calculate metrics using the original method
        metrics = evaluate_metric(results_matrix)

        print(f"Incremental avg accuracy: {metrics['Incre_avg_acc']}")
        print(f"*** Avg accuracy *** {metrics['Avg_acc']}")
        print(f"Incremental avg forget: {metrics['Incre_avg_forget']}")
        print(f"*** Avg forget *** {metrics['Avg_forget']}")
        print(f"6Q Incremental avg accuracy: {metrics['Incre_avg_acc_6Q']}")
        print(f"*** _6Q Avg accuracy *** {metrics['Avg_acc_6Q']}")
        print(f"_6Q Incremental avg forget: {metrics['Incre_avg_forget_6Q']}")
        print(f"*** _6Q Avg forget *** {metrics['Avg_forget_6Q']}")

        return metrics

    return None


def display_all_experiment_summary(search_results: Dict[str, Any]):
    """Display summary of all experiments with the limited data we have."""

    print("\n" + "=" * 80)
    print("ALL EXPERIMENTS SUMMARY (LIMITED DATA AVAILABLE)")
    print("=" * 80)

    print("Note: Individual performance matrices were not saved during the search.")
    print("Only the final best result matrix is available for detailed analysis.")
    print("The metrics below are from the optimization framework calculations.")

    all_results = search_results['all_results']

    # Sort by objective score
    sorted_results = sorted(all_results, key=lambda x: x['objective_score'], reverse=True)

    print(f"\n{'Rank':<4} {'Exp':<3} {'Obj Score':<10} {'Final Acc':<9} {'Forgetting':<10} {'Transfer':<9} {'Task Order (first 3)':<30}")
    print("-" * 90)

    for rank, result in enumerate(sorted_results, 1):
        first_three = ' → '.join([t.replace('q_', '') for t in result['task_order'][:3]])

        print(f"{rank:<4} {rank:<3} {result['objective_score']:<10.3f} "
              f"{result['metrics']['final_avg_accuracy']:<9.2f} "
              f"{result['metrics']['avg_forgetting']:<10.2f} "
              f"{result['metrics']['avg_forward_transfer']:<9.2f} "
              f"{first_three:<30}")

    # Print statistics
    obj_scores = [r['objective_score'] for r in all_results]
    print(f"\nObjective Score Statistics:")
    print(f"  Best: {max(obj_scores):.4f}")
    print(f"  Worst: {min(obj_scores):.4f}")
    print(f"  Mean: {np.mean(obj_scores):.4f}")
    print(f"  Std: {np.std(obj_scores):.4f}")


def identify_data_limitations():
    """Identify what data is missing and what needs to be fixed."""

    print("\n" + "=" * 80)
    print("DATA LIMITATIONS AND REQUIRED FIXES")
    print("=" * 80)

    print("CURRENT SITUATION:")
    print("✓ Search completed with 23 experiments")
    print("✓ Objective scores and framework metrics available for all experiments")
    print("✓ One performance matrix available (from the last/best experiment)")
    print("✓ One results matrix available (from the last/best experiment)")
    print("✗ Individual performance matrices for each experiment NOT saved")
    print("✗ Individual detailed metrics for each experiment NOT available")

    print("\nWHY THIS HAPPENED:")
    print("- The VL-T5 training overwrites the same files (performance_matrix.npy, results_matrix.json)")
    print("- The optimization framework didn't save individual experiment data")
    print("- Logs were not properly captured during training")

    print("\nTO GET REAL INDIVIDUAL METRICS:")
    print("1. Modify the system to save individual experiment results")
    print("2. Re-run experiments with proper checkpointing")
    print("3. Or extract from training logs if they contain the detailed output")


def main():
    """Main analysis function."""

    print("EXTRACTING REAL METRICS FROM AVAILABLE DATA")
    print("=" * 80)

    # Analyze what we have
    search_results, performance_matrix, results_matrix = analyze_available_data()

    if not search_results:
        print("Cannot proceed without search results")
        return

    # Display the best experiment (only one with real detailed data)
    if performance_matrix is not None and results_matrix is not None:
        real_metrics = display_best_experiment_details(search_results, performance_matrix, results_matrix)

    # Display summary of all experiments (with limited data)
    display_all_experiment_summary(search_results)

    # Explain limitations
    identify_data_limitations()

    print("\n" + "=" * 80)
    print("CONCLUSION")
    print("=" * 80)
    print("Real detailed metrics are only available for the best experiment.")
    print("To get individual metrics for all 23 experiments, the system needs to be")
    print("modified to save individual results during training.")


if __name__ == "__main__":
    main()
