"""
Test script to verify the actual VL-T5 training integration works.

This script tests a minimal CL experiment with a small subset of tasks
to verify that the integration with the actual training system works.
"""

import sys
import time
import logging
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from task_order_optimization.cl_experiment_adapter import run_my_actual_cl_experiment
from Question_type import set_custom_task_order, get_task_order, reset_task_order

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_minimal_training():
    """Test with a minimal task order to verify training works."""

    print("="*60)
    print("TESTING MINIMAL VL-T5 TRAINING INTEGRATION")
    print("="*60)

    # Use a small subset of tasks for quick testing
    test_task_order = ['q_recognition', 'q_color']  # Just 2 tasks for quick test

    # Full canonical list (all 10 tasks)
    canonical_task_list = [
        'q_recognition', 'q_causal', 'q_color', 'q_commonsense',
        'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge'
    ]

    print(f"Test task order: {test_task_order}")
    print(f"Canonical task list: {canonical_task_list}")
    print(f"Expected matrix shape: ({len(test_task_order)}, {len(canonical_task_list)})")

    # Record start time
    start_time = time.time()

    try:
        # Run the experiment
        print("\nStarting CL experiment...")
        performance_matrix = run_my_actual_cl_experiment(
            test_task_order,
            canonical_task_list
        )

        # Record end time
        end_time = time.time()
        duration = end_time - start_time

        print(f"\nExperiment completed in {duration:.2f} seconds")

        # Verify results
        if performance_matrix is not None:
            print(f"✓ Performance matrix shape: {performance_matrix.shape}")
            print(f"✓ Matrix values range: [{performance_matrix.min():.2f}, {performance_matrix.max():.2f}]")

            # Print the matrix
            print("\nPerformance Matrix:")
            print("Rows = training steps, Columns = tasks")
            print("Task order:", [canonical_task_list[i] for i in range(len(canonical_task_list))])

            # Note: The actual training ran all 10 tasks, not just the test subset
            actual_task_order = ['q_recognition', 'q_causal', 'q_color', 'q_commonsense',
                               'q_subcategory', 'q_location', 'q_count', 'q_action', 'q_type', 'q_judge']

            for step in range(performance_matrix.shape[0]):
                if step < len(actual_task_order):
                    step_task = actual_task_order[step]
                    print(f"After training {step_task}: {performance_matrix[step, :]}")
                else:
                    print(f"Step {step}: {performance_matrix[step, :]}")

            # Check for reasonable values
            final_row = performance_matrix[-1, :]
            trained_tasks_performance = []

            for task in test_task_order:
                if task in canonical_task_list:
                    task_idx = canonical_task_list.index(task)
                    perf = final_row[task_idx]
                    trained_tasks_performance.append(perf)
                    print(f"Final performance on {task}: {perf:.2f}%")

            if all(perf > 0 for perf in trained_tasks_performance):
                print("✓ All trained tasks have positive performance")
            else:
                print("⚠ Some trained tasks have zero performance")

            # Check that untrained tasks have zero performance
            untrained_zero = True
            for i, task in enumerate(canonical_task_list):
                if task not in test_task_order and final_row[i] > 0:
                    print(f"⚠ Untrained task {task} has non-zero performance: {final_row[i]}")
                    untrained_zero = False

            if untrained_zero:
                print("✓ Untrained tasks have zero performance")

            return True

        else:
            print("❌ Experiment returned None")
            return False

    except Exception as e:
        print(f"❌ Experiment failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_task_order_setting():
    """Test that task order setting works correctly."""

    print("\n" + "="*60)
    print("TESTING TASK ORDER SETTING")
    print("="*60)

    try:
        # Get original order
        original_order = get_task_order()
        print(f"Original task order: {original_order}")

        # Set custom order
        custom_order = ['q_color', 'q_recognition', 'q_location']
        set_custom_task_order(custom_order)

        # Verify it was set
        current_order = get_task_order()
        print(f"Custom task order: {current_order}")

        if current_order == custom_order:
            print("✓ Custom task order set correctly")
        else:
            print("❌ Custom task order not set correctly")
            return False

        # Reset and verify
        reset_task_order()
        reset_order = get_task_order()
        print(f"Reset task order: {reset_order}")

        if reset_order == original_order:
            print("✓ Task order reset correctly")
            return True
        else:
            print("❌ Task order not reset correctly")
            return False

    except Exception as e:
        print(f"❌ Task order setting failed: {str(e)}")
        return False


def check_vl_t5_environment():
    """Check if VL-T5 environment is properly set up."""

    print("\n" + "="*60)
    print("CHECKING VL-T5 ENVIRONMENT")
    print("="*60)

    # Check if VL-T5 directory exists
    vl_t5_path = Path("../VL-T5")
    if not vl_t5_path.exists():
        vl_t5_path = Path("VL-T5")

    if vl_t5_path.exists():
        print(f"✓ VL-T5 directory found: {vl_t5_path.absolute()}")
    else:
        print("❌ VL-T5 directory not found")
        return False

    # Check if training script exists
    script_path = vl_t5_path / "scripts" / "VQACL_train1.sh"
    if script_path.exists():
        print(f"✓ Training script found: {script_path}")
    else:
        print(f"❌ Training script not found: {script_path}")
        return False

    # Check if source files exist
    src_path = vl_t5_path / "src" / "vqacl2.py"
    if src_path.exists():
        print(f"✓ Source file found: {src_path}")
    else:
        print(f"❌ Source file not found: {src_path}")
        return False

    return True


def main():
    """Run all tests."""

    print("TASK ORDER OPTIMIZATION - ACTUAL TRAINING TEST")
    print("=" * 80)

    # Check environment first
    if not check_vl_t5_environment():
        print("\n❌ VL-T5 environment check failed")
        print("Please ensure VL-T5 is properly set up before running training tests")
        return False

    # Test task order setting
    if not test_task_order_setting():
        print("\n❌ Task order setting test failed")
        return False

    # Ask user if they want to run actual training (takes time)
    print("\n" + "="*60)
    print("ACTUAL TRAINING TEST")
    print("="*60)
    print("The next test will run actual VL-T5 training, which may take significant time.")
    print("This will train on 2 tasks (q_recognition, q_color) for testing purposes.")

    response = input("\nDo you want to proceed with actual training test? (y/N): ").strip().lower()

    if response in ['y', 'yes']:
        print("\nProceeding with actual training test...")
        if test_minimal_training():
            print("\n✅ ALL TESTS PASSED!")
            print("The task order optimization framework is ready for actual use.")
            return True
        else:
            print("\n❌ Training test failed")
            return False
    else:
        print("\nSkipping actual training test.")
        print("✅ Environment and task order setting tests passed.")
        print("To test actual training, run this script again and choose 'y'.")
        return True


if __name__ == "__main__":
    success = main()

    if success:
        print("\n" + "="*80)
        print("NEXT STEPS:")
        print("="*80)
        print("1. For full optimization, run: python main_search_loop.py")
        print("2. Adjust search parameters in main_search_loop.py as needed")
        print("3. Monitor progress in task_order_search.log")
        print("4. Results will be saved to task_order_search_results_*.json")

    sys.exit(0 if success else 1)
