"""
Resume Task Order Search from Previous Results

This script allows resuming the task order optimization search from
previously saved results and experiment checkpoints.
"""

import json
import numpy as np
import time
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import framework components
from order_search_framework import (
    ExperimentDataStore, 
    HeuristicProposer, 
    ExperimentResult
)
from main_search_loop import TaskOrderSearchManager


def load_previous_results(results_file: str) -> Optional[Dict[str, Any]]:
    """Load previous search results from JSON file."""
    
    results_path = Path(results_file)
    if not results_path.exists():
        print(f"Results file not found: {results_file}")
        return None
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        print(f"Loaded previous results from: {results_file}")
        print(f"Previous search had {results['search_metadata']['total_experiments']} experiments")
        print(f"Previous search took {results['search_metadata']['total_time_seconds'] / 3600:.1f} hours")
        
        return results
        
    except Exception as e:
        print(f"Error loading results file: {e}")
        return None


def load_experiment_checkpoints() -> Dict[int, Dict[str, Any]]:
    """Load individual experiment checkpoints if available."""
    
    checkpoint_dir = Path("task_order_optimization/experiment_checkpoints")
    checkpoints = {}
    
    if not checkpoint_dir.exists():
        print("No experiment checkpoints directory found")
        return checkpoints
    
    # Load individual experiment data
    for data_file in checkpoint_dir.glob("experiment_*_data.json"):
        try:
            with open(data_file, 'r') as f:
                experiment_data = json.load(f)
            
            exp_id = experiment_data['experiment_id']
            checkpoints[exp_id] = experiment_data
            
        except Exception as e:
            print(f"Error loading checkpoint {data_file}: {e}")
    
    if checkpoints:
        print(f"Loaded {len(checkpoints)} experiment checkpoints")
    else:
        print("No experiment checkpoints found")
    
    return checkpoints


def restore_data_store(previous_results: Dict[str, Any], 
                      checkpoints: Dict[int, Dict[str, Any]]) -> ExperimentDataStore:
    """Restore the ExperimentDataStore from previous results."""
    
    all_tasks = previous_results['search_metadata']['all_tasks']
    objective_weights = previous_results['search_metadata']['objective_weights']
    
    # Create new data store
    data_store = ExperimentDataStore(all_tasks)
    
    # Restore experiments
    all_results = previous_results['all_results']
    
    for i, result in enumerate(all_results):
        exp_id = i + 1
        task_order = result['task_order']
        
        # Try to get performance matrix from checkpoints first
        if exp_id in checkpoints:
            performance_matrix = np.array(checkpoints[exp_id]['performance_matrix'])
            print(f"Restored experiment {exp_id} from checkpoint")
        else:
            # Fall back to the matrix from JSON (only available for best result)
            if 'performance_matrix' in result:
                performance_matrix = np.array(result['performance_matrix'])
                print(f"Restored experiment {exp_id} from JSON results")
            else:
                # Use the best result matrix as fallback
                performance_matrix = np.array(previous_results['best_result']['performance_matrix'])
                print(f"Warning: Using best result matrix for experiment {exp_id}")
        
        # Add experiment to data store
        data_store.add_experiment(
            task_order, performance_matrix, all_tasks, objective_weights
        )
    
    print(f"Restored {len(data_store.results)} experiments to data store")
    return data_store


def resume_search(previous_results_file: str, 
                 additional_iterations: int = 5,
                 orders_per_iteration: int = 2) -> Dict[str, Any]:
    """Resume the task order search from previous results."""
    
    print("=" * 80)
    print("RESUMING TASK ORDER OPTIMIZATION SEARCH")
    print("=" * 80)
    
    # Load previous results
    previous_results = load_previous_results(previous_results_file)
    if not previous_results:
        return {}
    
    # Load experiment checkpoints
    checkpoints = load_experiment_checkpoints()
    
    # Restore data store
    data_store = restore_data_store(previous_results, checkpoints)
    
    # Get configuration from previous search
    all_tasks = previous_results['search_metadata']['all_tasks']
    objective_weights = previous_results['search_metadata']['objective_weights']
    
    print(f"\nResuming search with:")
    print(f"- {len(all_tasks)} tasks")
    print(f"- {len(data_store.results)} previous experiments")
    print(f"- {additional_iterations} additional iterations")
    print(f"- {orders_per_iteration} orders per iteration")
    
    # Create search manager with restored data
    search_manager = TaskOrderSearchManager(
        all_tasks=all_tasks,
        objective_weights=objective_weights,
        max_iterations=additional_iterations,
        orders_per_iteration=orders_per_iteration
    )
    
    # Replace the data store with the restored one
    search_manager.data_store = data_store
    
    # Get current best result
    current_best = data_store.get_best_result()
    if current_best:
        print(f"\nCurrent best result:")
        print(f"- Task order: {current_best.task_order}")
        print(f"- Objective score: {current_best.objective_score:.4f}")
        print(f"- Final accuracy: {current_best.metrics.get('final_avg_accuracy', 'N/A')}")
    
    # Continue the search
    print(f"\nContinuing search...")
    results = search_manager.run_search()
    
    return results


def main():
    """Main function for resuming search."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="Resume task order optimization search")
    parser.add_argument("--results-file", 
                       default="task_order_search_results_20250601_060319.json",
                       help="Previous results JSON file")
    parser.add_argument("--iterations", type=int, default=5,
                       help="Additional iterations to run")
    parser.add_argument("--orders-per-iteration", type=int, default=2,
                       help="Orders to evaluate per iteration")
    
    args = parser.parse_args()
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('task_order_search_resumed.log'),
            logging.StreamHandler()
        ]
    )
    
    # Resume search
    results = resume_search(
        args.results_file,
        args.iterations,
        args.orders_per_iteration
    )
    
    if results:
        print("\n" + "=" * 80)
        print("RESUMED SEARCH COMPLETED!")
        print("=" * 80)
        
        if results['best_result']['task_order']:
            print(f"Best task order: {results['best_result']['task_order']}")
            print(f"Best objective score: {results['best_result']['objective_score']:.4f}")
        
        print(f"Total experiments (including previous): {results['search_metadata']['total_experiments']}")
    else:
        print("Failed to resume search")


if __name__ == "__main__":
    main()
